#!/usr/bin/env python3
"""
Script to convert methodology text file to DOCX with proper APA 7th edition formatting
"""

import os
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_LINE_SPACING
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn

def setup_apa_styles(doc):
    """Set up APA 7th edition styles"""
    
    # Set document margins (1 inch all around)
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1)
        section.right_margin = Inches(1)
    
    # Normal style (12pt Times New Roman, double-spaced)
    normal_style = doc.styles['Normal']
    normal_font = normal_style.font
    normal_font.name = 'Times New Roman'
    normal_font.size = Pt(12)
    
    normal_paragraph = normal_style.paragraph_format
    normal_paragraph.line_spacing_rule = WD_LINE_SPACING.DOUBLE
    normal_paragraph.first_line_indent = Inches(0.5)
    normal_paragraph.space_before = Pt(0)
    normal_paragraph.space_after = Pt(0)
    
    # Level 1 Heading (Centered, Bold, 14pt)
    try:
        heading1_style = doc.styles.add_style('APA Heading 1', WD_STYLE_TYPE.PARAGRAPH)
    except:
        heading1_style = doc.styles['APA Heading 1']
    
    heading1_font = heading1_style.font
    heading1_font.name = 'Times New Roman'
    heading1_font.size = Pt(14)
    heading1_font.bold = True
    
    heading1_paragraph = heading1_style.paragraph_format
    heading1_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
    heading1_paragraph.line_spacing_rule = WD_LINE_SPACING.DOUBLE
    heading1_paragraph.space_before = Pt(0)
    heading1_paragraph.space_after = Pt(0)
    
    # Level 2 Heading (Flush Left, Bold, 14pt)
    try:
        heading2_style = doc.styles.add_style('APA Heading 2', WD_STYLE_TYPE.PARAGRAPH)
    except:
        heading2_style = doc.styles['APA Heading 2']
    
    heading2_font = heading2_style.font
    heading2_font.name = 'Times New Roman'
    heading2_font.size = Pt(14)
    heading2_font.bold = True
    
    heading2_paragraph = heading2_style.paragraph_format
    heading2_paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
    heading2_paragraph.line_spacing_rule = WD_LINE_SPACING.DOUBLE
    heading2_paragraph.space_before = Pt(0)
    heading2_paragraph.space_after = Pt(0)
    
    # Level 3 Heading (Flush Left, Bold Italic, 14pt)
    try:
        heading3_style = doc.styles.add_style('APA Heading 3', WD_STYLE_TYPE.PARAGRAPH)
    except:
        heading3_style = doc.styles['APA Heading 3']
    
    heading3_font = heading3_style.font
    heading3_font.name = 'Times New Roman'
    heading3_font.size = Pt(14)
    heading3_font.bold = True
    heading3_font.italic = True
    
    heading3_paragraph = heading3_style.paragraph_format
    heading3_paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
    heading3_paragraph.line_spacing_rule = WD_LINE_SPACING.DOUBLE
    heading3_paragraph.space_before = Pt(0)
    heading3_paragraph.space_after = Pt(0)
    
    # Level 4 Heading (Indented, Bold, 12pt, ending with period)
    try:
        heading4_style = doc.styles.add_style('APA Heading 4', WD_STYLE_TYPE.PARAGRAPH)
    except:
        heading4_style = doc.styles['APA Heading 4']
    
    heading4_font = heading4_style.font
    heading4_font.name = 'Times New Roman'
    heading4_font.size = Pt(12)
    heading4_font.bold = True
    
    heading4_paragraph = heading4_style.paragraph_format
    heading4_paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
    heading4_paragraph.line_spacing_rule = WD_LINE_SPACING.DOUBLE
    heading4_paragraph.first_line_indent = Inches(0.5)
    heading4_paragraph.space_before = Pt(0)
    heading4_paragraph.space_after = Pt(0)

def insert_image(doc, image_path, width=6.0):
    """Insert image with proper APA formatting"""
    if os.path.exists(image_path):
        paragraph = doc.add_paragraph()
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
        run.add_picture(image_path, width=Inches(width))
        
        # Remove spacing around image
        paragraph_format = paragraph.paragraph_format
        paragraph_format.space_before = Pt(0)
        paragraph_format.space_after = Pt(0)
        paragraph_format.line_spacing_rule = WD_LINE_SPACING.SINGLE
    else:
        # Add placeholder if image not found
        paragraph = doc.add_paragraph(f"[IMAGE NOT FOUND: {image_path}]")
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

def create_table(doc, table_data, title):
    """Create APA-formatted table"""
    # Add table title
    title_paragraph = doc.add_paragraph()
    title_run = title_paragraph.add_run(title)
    title_run.font.name = 'Times New Roman'
    title_run.font.size = Pt(12)
    title_run.font.bold = True
    title_paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
    
    # Create table
    table = doc.add_table(rows=len(table_data), cols=len(table_data[0]))
    table.style = 'Table Grid'
    
    # Fill table data
    for i, row_data in enumerate(table_data):
        row = table.rows[i]
        for j, cell_data in enumerate(row_data):
            cell = row.cells[j]
            cell.text = str(cell_data)
            
            # Format cell text
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    run.font.name = 'Times New Roman'
                    run.font.size = Pt(12)
    
    # Add spacing after table
    doc.add_paragraph()

def process_methodology_file():
    """Process the methodology file and create DOCX"""
    
    # Read the formatted methodology file
    with open('methodology_formatted_for_word.txt', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Create new document
    doc = Document()
    
    # Setup APA styles
    setup_apa_styles(doc)
    
    # Split content into lines
    lines = content.split('\n')
    
    # Skip the formatting instructions at the beginning
    start_index = 0
    for i, line in enumerate(lines):
        if line.strip() == "Method":
            start_index = i
            break
    
    # Process each line
    i = start_index
    while i < len(lines):
        line = lines[i].strip()
        
        if not line:
            i += 1
            continue
        
        # Check for headings and special content
        if line == "Method":
            paragraph = doc.add_paragraph(line)
            paragraph.style = doc.styles['APA Heading 1']
        
        elif line in ["Grading Criteria", "Task Definition", "System Architecture and Design"]:
            paragraph = doc.add_paragraph(line)
            paragraph.style = doc.styles['APA Heading 2']
        
        elif line == "Experiments":
            paragraph = doc.add_paragraph(line)
            paragraph.style = doc.styles['APA Heading 1']
        
        elif line == "Performance Metrics and Statistical Analysis":
            paragraph = doc.add_paragraph(line)
            paragraph.style = doc.styles['APA Heading 1']
        
        elif line == "Limitations and Future Directions":
            paragraph = doc.add_paragraph(line)
            paragraph.style = doc.styles['APA Heading 1']
        
        elif line in ["The Fluency Module", "The Correctness Module", "Datasets", 
                     "Training Procedures and Model Optimization", "Comparison Experiments",
                     "Validation of Effects on Writing Learning Assistance"]:
            paragraph = doc.add_paragraph(line)
            paragraph.style = doc.styles['APA Heading 3']
        
        elif line.endswith(".") and ("Training and Evaluation Corpus" in line or 
                                   "Reference Corpus" in line or
                                   "BERT-Based Architecture Implementation" in line or
                                   "T5-Based Error Detection" in line or
                                   "Error Quantification" in line or
                                   "Fluency Model Training Strategy" in line or
                                   "Correctness Model Training Strategy" in line or
                                   "Evaluation Metric" in line or
                                   "Baseline Model Comparisons" in line or
                                   "Method" in line or "Participants" in line or
                                   "Materials" in line or "Procedure" in line or
                                   "Statistical Analysis" in line):
            paragraph = doc.add_paragraph(line)
            paragraph.style = doc.styles['APA Heading 4']
        
        # Handle image insertions
        elif line.startswith("[INSERT FIGURE"):
            figure_num = line.split("FIGURE ")[1].split(" ")[0]
            image_filename = f"figure{figure_num}_"
            
            if "system_architecture" in line:
                image_path = "figures/figure1_system_architecture.png"
            elif "bert_architecture" in line:
                image_path = "figures/figure2_bert_architecture.png"
            elif "t5_workflow" in line:
                image_path = "figures/figure3_t5_workflow.png"
            elif "training_progress" in line:
                image_path = "figures/figure4_training_progress.png"
            elif "experimental_design" in line:
                image_path = "figures/figure5_experimental_design.png"
            elif "performance_analysis" in line:
                image_path = "figures/figure6_performance_analysis.png"
            elif "system_integration" in line:
                image_path = "figures/figure7_system_integration.png"
            else:
                image_path = f"figures/figure{figure_num}.png"
            
            insert_image(doc, image_path)
        
        # Handle table insertions
        elif line.startswith("[INSERT TABLE"):
            table_num = line.split("TABLE ")[1].split(" ")[0]
            
            # Skip to table title and data
            i += 1
            while i < len(lines) and not lines[i].strip().startswith("Table"):
                i += 1
            
            if i < len(lines):
                table_title = lines[i].strip()
                
                # Collect table data
                table_data = []
                i += 1
                while i < len(lines) and lines[i].strip():
                    row_line = lines[i].strip()
                    if not row_line.startswith("_") and not row_line.startswith("*"):
                        # Parse table row
                        if "    " in row_line:  # Multi-column format
                            parts = [part.strip() for part in row_line.split("    ") if part.strip()]
                            if len(parts) > 1:
                                table_data.append(parts)
                        elif row_line and not row_line.startswith("Table"):
                            table_data.append([row_line])
                    i += 1
                
                if table_data:
                    create_table(doc, table_data, table_title)
                i -= 1  # Adjust for the outer loop increment
        
        # Handle figure captions
        elif line.startswith("Figure "):
            paragraph = doc.add_paragraph(line)
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            run = paragraph.runs[0]
            run.font.name = 'Times New Roman'
            run.font.size = Pt(12)
            run.font.bold = True
        
        # Regular paragraph text
        else:
            paragraph = doc.add_paragraph(line)
            paragraph.style = doc.styles['Normal']
        
        i += 1
    
    # Save the document
    output_filename = "methodology_apa_formatted.docx"
    doc.save(output_filename)
    print(f"Document saved as: {output_filename}")
    
    return output_filename

if __name__ == "__main__":
    try:
        output_file = process_methodology_file()
        print(f"Successfully created DOCX file: {output_file}")
        print("The document follows APA 7th edition formatting guidelines:")
        print("- 12pt Times New Roman font for body text")
        print("- 14pt bold headings for levels 1-3")
        print("- 12pt bold headings for level 4")
        print("- Double-spaced text with 1-inch margins")
        print("- Proper heading hierarchy without numbering")
        print("- Images inserted at appropriate locations")
        print("- Tables formatted according to APA style")
    except Exception as e:
        print(f"Error creating DOCX file: {e}")
        import traceback
        traceback.print_exc()
