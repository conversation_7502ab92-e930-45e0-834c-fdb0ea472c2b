<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dual-Model System Architecture Figures</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/d3/7.8.5/d3.min.js"></script>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .figure {
            margin: 40px 0;
            page-break-inside: avoid;
        }
        
        .figure-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 10px;
            text-align: center;
            color: #333;
        }
        
        .figure-caption {
            font-size: 12px;
            color: #666;
            text-align: center;
            margin-top: 15px;
            font-style: italic;
        }
        
        .architecture-diagram {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            margin: 20px 0;
        }
        
        .flow-box {
            fill: #e8f4f8;
            stroke: #2c3e50;
            stroke-width: 2;
            rx: 10;
        }
        
        .model-box {
            fill: #fff3cd;
            stroke: #856404;
            stroke-width: 2;
            rx: 8;
        }
        
        .output-box {
            fill: #d4edda;
            stroke: #155724;
            stroke-width: 2;
            rx: 8;
        }
        
        .arrow {
            stroke: #495057;
            stroke-width: 2;
            fill: none;
            marker-end: url(#arrowhead);
        }
        
        .text-label {
            font-size: 12px;
            font-weight: bold;
            text-anchor: middle;
            dominant-baseline: middle;
            fill: #333;
        }
        
        .small-text {
            font-size: 10px;
            text-anchor: middle;
            dominant-baseline: middle;
            fill: #666;
        }
        
        .metrics-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 12px;
        }
        
        .metrics-table th,
        .metrics-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        
        .metrics-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .performance-chart {
            width: 100%;
            height: 300px;
            margin: 20px 0;
        }
        
        .bar {
            fill: #2c3e50;
            opacity: 0.8;
        }
        
        .bar:hover {
            opacity: 1;
        }
        
        .axis {
            font-size: 10px;
        }
        
        .axis-label {
            font-size: 12px;
            font-weight: bold;
        }
        
        .grid line {
            stroke: #e0e0e0;
            stroke-width: 1;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .tab {
            padding: 10px 20px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            color: #495057;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            background: #2c3e50;
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .neural-network {
            width: 100%;
            height: 500px;
            margin: 20px 0;
        }
        
        .neuron {
            fill: #3498db;
            stroke: #2980b9;
            stroke-width: 2;
        }
        
        .connection {
            stroke: #95a5a6;
            stroke-width: 1;
            opacity: 0.6;
        }
        
        .layer-label {
            font-size: 12px;
            font-weight: bold;
            text-anchor: middle;
            fill: #2c3e50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #2c3e50; margin-bottom: 40px;">
            Dual-Model System Architecture for Language Assessment
        </h1>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('overview')">System Overview</button>
            <button class="tab" onclick="showTab('bert')">BERT Architecture</button>
            <button class="tab" onclick="showTab('t5')">T5 Workflow</button>
            <button class="tab" onclick="showTab('training')">Training Progress</button>
            <button class="tab" onclick="showTab('experimental')">Experimental Design</button>
            <button class="tab" onclick="showTab('performance')">Performance Analysis</button>
            <button class="tab" onclick="showTab('validation')">System Integration</button>
        </div>
        
        <!-- Figure 1: System Overview -->
        <div id="overview" class="tab-content active">
            <div class="figure">
                <div class="figure-title">Figure 1. Dual-Model System Architecture Overview</div>
                <svg class="architecture-diagram" id="system-overview"></svg>
                <div class="figure-caption">
                    Complete dual-model system architecture, demonstrating the parallel processing workflow where input text undergoes simultaneous evaluation through both assessment pathways. The diagram shows the preprocessing pipeline feeding raw text into both the BERT-based fluency assessment module and the T5-based correctness evaluation module. The architecture emphasizes the complementary nature of the two evaluation streams, where the fluency module captures semantic coherence and natural language flow through transformer-based attention mechanisms, while the correctness module performs sequence-to-sequence error detection and correction. The figure displays the final score integration process, where weighted combinations of fluency and correctness scores (w₁=0.6, w₂=0.4) produce comprehensive writing quality assessments. This visualization demonstrates how the system maintains assessment objectivity while providing multidimensional feedback that addresses both expressive quality and linguistic accuracy.
                </div>
            </div>
        </div>
        
        <!-- Figure 2: BERT Architecture -->
        <div id="bert" class="tab-content">
            <div class="figure">
                <div class="figure-title">Figure 2. BERT-Based Fluency Assessment Architecture</div>
                <svg class="neural-network" id="bert-architecture"></svg>
                <div class="figure-caption">
                    Detailed architecture of the BERT-based fluency assessment model, illustrating the complete processing pipeline from input tokenization through multilayer transformer encoding to final score prediction. The diagram shows how input text undergoes WordPiece tokenization, creating token embeddings that include positional and segment encodings. The visualization demonstrates the 12-layer transformer architecture, with each layer containing multi-head self-attention mechanisms and feed-forward networks. The figure emphasizes how the [CLS] token serves as the global sequence representation, capturing semantic coherence across the entire input text. The regression head architecture is detailed, showing the progressive dimensionality reduction through fully connected layers with dropout regularization (768→512→256→128→1), culminating in the final fluency score prediction. This visualization highlights the model's ability to process contextual relationships and semantic patterns that contribute to natural language fluency assessment.
                </div>
            </div>
            
        </div>

        <!-- Figure 3: T5 Workflow -->
        <div id="t5" class="tab-content">
            <div class="figure">
                <div class="figure-title">Figure 3. T5-Based Correctness Assessment Workflow</div>
                <svg class="architecture-diagram" id="t5-workflow"></svg>
                <div class="figure-caption">
                    Comprehensive T5-based correctness assessment workflow, showcasing the complete error detection and correction process from input text analysis to final score computation. The diagram illustrates how input paragraphs undergo systematic preprocessing, including text normalization and tokenization using the T5Tokenizer. The visualization shows the encoder-decoder architecture, where the encoder generates contextualized representations of the input text, capturing grammatical patterns and potential error locations. The decoder component produces corrected sequences by generating appropriate grammatical alternatives for identified errors. The figure details the differential analysis component, which performs token-level comparison between original and corrected text to identify specific corrections, categorize error types (grammatical, mechanical, syntactic), and quantify the error ratio. The exponential decay scoring function is visualized, demonstrating how the correctness score (S_c = 100 × e^(-2.5 × Error_Ratio)) decreases proportionally with error frequency while maintaining sensitivity to incremental improvements. This comprehensive workflow visualization emphasizes the transformation-based approach to error detection and the systematic scoring mechanism that provides reliable correctness assessments.
                </div>
            </div>
        </div>

        <!-- Figure 4: Training Progress -->
        <div id="training" class="tab-content">
            <div class="figure">
                <div class="figure-title">Figure 4. Fluency Model Training Progress</div>
                <svg class="performance-chart" id="training-progress"></svg>
                <div class="figure-caption">
                    Comprehensive training progress analysis for the fluency assessment model, displaying multiple performance metrics across training epochs. The visualization includes loss curves showing the progression of Mean Squared Error (MSE) for both training and validation sets, demonstrating the model's learning convergence and generalization capability. The figure illustrates the learning rate schedule, showing the linear warmup phase followed by systematic decay, optimizing the training dynamics for stable convergence. Additional subplots display the evolution of evaluation metrics including Mean Absolute Error (MAE), R-squared coefficient, and Root Mean Square Error (RMSE) throughout the training process. The visualization emphasizes the model's ability to achieve stable performance with minimal overfitting, as evidenced by the parallel progression of training and validation metrics. This comprehensive training analysis demonstrates the effectiveness of the progressive batch size strategy (batch size: 4→16) and the AdamW optimizer (β₁=0.9, β₂=0.999, ε=1×10⁻⁸, learning rate: 5×10⁻⁵) in achieving optimal model performance for fluency assessment tasks.
                </div>
            </div>
        </div>
        

        <!-- Figure 5: Experimental Design -->
        <div id="experimental" class="tab-content">
            <div class="figure">
                <div class="figure-title">Figure 5. Experimental Design and Results Visualization</div>
                <svg class="architecture-diagram" id="experimental-design"></svg>
                <div class="figure-caption">
                    Comprehensive experimental design framework and key results from the validation study. The diagram shows the randomized controlled trial structure, including participant flow from recruitment through final assessment (n=90 Economics undergraduates, aged 18-21 years, balanced gender distribution: 45 male, 45 female). Performance comparison charts display mean writing scores across the three assessment phases for both control and experimental groups, with error bars indicating standard errors. Pre-test baseline equivalence is demonstrated (Control: fluency 6.85±1.13, correctness 6.73±1.08; Experimental: fluency 6.92±1.44, correctness 6.81±1.39, all p>0.05). Post-test results show significant improvement in experimental group (Control improvement: 0.65±1.23, d=0.32; Experimental improvement: 2.74±1.87, d=1.28, p<0.001). Statistical significance indicators highlight the superior improvement in the experimental group receiving automated feedback. The visualization includes effect size calculations and confidence intervals to demonstrate the practical significance of the intervention effects. Quality assurance procedures are illustrated, including inter-rater reliability protocols (Cronbach's α=0.847 for fluency, 0.823 for correctness), stratified sampling strategies, and statistical analysis frameworks using SPSS 26.0.
                </div>
            </div>
        </div>

        <!-- Figure 6: Performance Analysis -->
        <div id="performance" class="tab-content">
            <div class="figure">
                <div class="figure-title">Figure 6. Score Distribution and Correlation Analysis</div>
                <div style="display: flex; gap: 20px;">
                    <div style="flex: 1;">
                        <svg class="performance-chart" id="fluency-distribution"></svg>
                        <div style="text-align: center; font-size: 12px; margin-top: 10px;">
                            <strong>Fluency Score Distribution</strong><br>
                            Mean: 7.23, SD: 1.47, R²: 0.891<br>
                            Correlation with Human: 0.944 [0.938, 0.950]
                        </div>
                    </div>
                    <div style="flex: 1;">
                        <svg class="performance-chart" id="error-distribution"></svg>
                        <div style="text-align: center; font-size: 12px; margin-top: 10px;">
                            <strong>Correctness Score Distribution</strong><br>
                            Mean: 65.60, SD: 12.41, SE: 1.85<br>
                            Precision: 0.891, Recall: 0.867, F1: 0.879
                        </div>
                    </div>
                </div>
                <div class="figure-caption">
                    Detailed analysis of score distributions and correlation patterns between automated and human assessments. The primary panels show histograms of fluency and correctness scores with overlaid normal distribution curves that reveal the underlying statistical patterns. The fluency distribution (left) demonstrates strong correlation with human expert ratings (r=0.944, 95% CI [0.938, 0.950]) with excellent predictive accuracy (MAE=0.142, RMSE=0.218, R²=0.891). The correctness distribution (right) shows robust error detection performance (Precision=0.891, Recall=0.867, F1-Score=0.879) with appropriate score distribution characteristics (mean=65.60, SD=12.41, SE=1.85). Scatter plots demonstrate the correlation between automated system scores and human expert ratings, with regression lines and correlation coefficients displayed. The figure includes residual analysis plots that validate the accuracy of automated scoring and identify potential bias patterns across different proficiency levels. Performance metrics quantify the model's ability to produce scores that align with expert human evaluations while maintaining sensitivity to different fluency and correctness levels.
                </div>
            </div>

            <div class="figure">
                <div class="figure-title">Dual-Model System Performance Comparison</div>
                <table class="metrics-table">
                    <tr><th>Model Type</th><th>Correlation</th><th>MAE</th><th>RMSE</th><th>Performance Rank</th></tr>
                    <tr style="background-color: #e8f5e8;"><td><strong>Dual-Model System</strong></td><td><strong>0.923</strong></td><td><strong>0.149</strong></td><td><strong>0.226</strong></td><td><strong>1</strong></td></tr>
                    <tr><td>BERT-only (Fluency)</td><td>0.844</td><td>0.187</td><td>0.267</td><td>2</td></tr>
                    <tr><td>Hybrid Feature-based</td><td>0.812</td><td>0.198</td><td>0.276</td><td>3</td></tr>
                    <tr><td>Traditional AWE</td><td>0.789</td><td>0.211</td><td>0.289</td><td>4</td></tr>
                    <tr><td>Rule-based (Correctness)</td><td>0.756</td><td>0.234</td><td>0.312</td><td>5</td></tr>
                </table>
                <div class="figure-caption">
                    Comparison of the dual-model system performance against baseline automated writing evaluation approaches. The table demonstrates the superior performance of the integrated dual-model approach (correlation=0.923, MAE=0.149, RMSE=0.226) compared to single-dimension systems including BERT-only fluency assessment (r=0.844), rule-based correctness evaluation (r=0.756), traditional feature-based AWE systems (r=0.789), and hybrid approaches (r=0.812). The dual-model system achieves the highest correlation with human expert judgments while maintaining the lowest error rates, validating the effectiveness of the integrated assessment framework that combines both fluency and correctness evaluation dimensions.
                </div>
            </div>
        </div>
        
        <!-- Figure 7: System Integration -->
        <div id="validation" class="tab-content">
            <div class="figure">
                <div class="figure-title">Figure 7. System Architecture and Integration Framework</div>
                <svg class="architecture-diagram" id="system-integration"></svg>
                <div class="figure-caption">
                    Comprehensive dual-model system architecture and integration framework, demonstrating the complete workflow from input processing through score generation and feedback delivery. The diagram shows the parallel processing pathways for fluency and correctness assessment, with detailed visualization of the BERT-based and T5-based neural network architectures. The fluency module processes input through 12 transformer layers with multi-head self-attention mechanisms (Attention(Q,K,V) = softmax(QK^T/√d_k)V), utilizing the [CLS] token embedding for global sequence representation and a 4-layer MLP regression head (768→512→256→128→1) with dropout regularization. The correctness module employs the FLAN-T5-large architecture for text-to-text generation (Corrected_Text = T5_Decoder(T5_Encoder(Original_Text))), performing differential analysis to calculate error ratios and apply exponential decay scoring (S_c = 100 × e^(-2.5 × Error_Ratio)). The figure displays the mathematical integration process using weighted combination (FinalScore = 0.6 × S_f + 0.4 × S_c) and confidence interval calculation procedures. Quality assurance mechanisms are illustrated, showing validation protocols and reliability monitoring systems that ensure consistent performance across diverse writing samples. Technical specifications include processing speed (<30 seconds per 400-word essay), memory requirements (8GB GPU), and scalability parameters (batch processing up to 50 essays).
                </div>
            </div>
            
            <div class="figure">
                <div class="figure-title">Statistical Validation and Performance Tables</div>
                <div style="display: flex; gap: 20px; margin-bottom: 20px;">
                    <div style="flex: 1;">
                        <h4>Table 3. Dual-Model System Performance Metrics</h4>
                        <table class="metrics-table">
                            <tr><th>Evaluation Dimension</th><th>Correlation</th><th>MAE</th><th>RMSE</th><th>R²</th></tr>
                            <tr><td>Fluency Assessment</td><td>0.944</td><td>0.142</td><td>0.218</td><td>0.891</td></tr>
                            <tr><td>Correctness Assessment</td><td>0.887</td><td>0.156</td><td>0.234</td><td>0.867</td></tr>
                            <tr style="background-color: #e8f5e8;"><td><strong>Composite Score</strong></td><td><strong>0.923</strong></td><td><strong>0.149</strong></td><td><strong>0.226</strong></td><td><strong>0.879</strong></td></tr>
                        </table>
                    </div>
                    <div style="flex: 1;">
                        <h4>Table 5. Writing Improvement Study Results</h4>
                        <table class="metrics-table">
                            <tr><th>Group</th><th>Pre-test</th><th>Post-test</th><th>Improvement</th><th>Effect Size (d)</th></tr>
                            <tr><td>Control (n=45)</td><td>13.58±1.95</td><td>14.23±2.11</td><td>0.65±1.23</td><td>0.32</td></tr>
                            <tr style="background-color: #e8f5e8;"><td><strong>Experimental (n=45)</strong></td><td><strong>13.73±2.12</strong></td><td><strong>16.47±2.34</strong></td><td><strong>2.74±1.87</strong></td><td><strong>1.28*</strong></td></tr>
                        </table>
                        <div style="font-size: 12px; margin-top: 5px;">*p < 0.001, indicating statistically significant improvement</div>
                    </div>
                </div>

                <div style="display: flex; gap: 20px;">
                    <div style="flex: 1;">
                        <h4>Table 6. Inter-rater Reliability and System Validation</h4>
                        <table class="metrics-table">
                            <tr><th>Metric</th><th>Value</th><th>Standard Error</th><th>95% CI</th></tr>
                            <tr><td>Fluency Inter-rater (α)</td><td>0.847</td><td>-</td><td>[0.823, 0.871]</td></tr>
                            <tr><td>Correctness Inter-rater (α)</td><td>0.823</td><td>-</td><td>[0.798, 0.848]</td></tr>
                            <tr><td>System-Human Correlation</td><td>0.923</td><td>0.003</td><td>[0.917, 0.929]</td></tr>
                            <tr><td>Test-Retest Reliability</td><td>0.912</td><td>0.004</td><td>[0.904, 0.920]</td></tr>
                        </table>
                    </div>
                    <div style="flex: 1;">
                        <h4>Cross-Validation Results (5-Fold)</h4>
                        <table class="metrics-table">
                            <tr><th>Fold</th><th>Correlation</th><th>RMSE</th><th>MAE</th></tr>
                            <tr><td>1</td><td>0.887</td><td>0.221</td><td>0.145</td></tr>
                            <tr><td>2</td><td>0.894</td><td>0.215</td><td>0.139</td></tr>
                            <tr><td>3</td><td>0.889</td><td>0.219</td><td>0.143</td></tr>
                            <tr><td>4</td><td>0.893</td><td>0.217</td><td>0.141</td></tr>
                            <tr><td>5</td><td>0.892</td><td>0.218</td><td>0.142</td></tr>
                            <tr style="background-color: #f0f0f0;"><td><strong>Mean</strong></td><td><strong>0.891</strong></td><td><strong>0.218</strong></td><td><strong>0.142</strong></td></tr>
                        </table>
                    </div>
                </div>
                <div class="figure-caption">
                    Comprehensive statistical validation results demonstrating the dual-model system's effectiveness across multiple evaluation criteria. Table 3 shows superior performance of the composite scoring approach compared to individual dimensions. Table 5 presents experimental validation results showing significant writing improvement in the group receiving automated feedback (d=1.28, p<0.001), indicating large practical effect size. Table 6 demonstrates excellent inter-rater reliability and system-human correlation. Cross-validation results show consistent performance across all folds, confirming robust generalization capability and system reliability.
                </div>
            </div>
        </div>
    </div>

    <script>
        // Define SVG markers for arrows
        function createArrowMarkers(svg) {
            const defs = svg.append("defs");
            
            defs.append("marker")
                .attr("id", "arrowhead")
                .attr("viewBox", "0 -5 10 10")
                .attr("refX", 8)
                .attr("refY", 0)
                .attr("markerWidth", 6)
                .attr("markerHeight", 6)
                .attr("orient", "auto")
                .append("path")
                .attr("d", "M0,-5L10,0L0,5")
                .attr("fill", "#495057");
        }
        
        // Figure 1: System Overview
        function createSystemOverview() {
            const svg = d3.select("#system-overview");
            const width = 800;
            const height = 400;
            
            createArrowMarkers(svg);
            
            // Input Processing
            svg.append("rect")
                .attr("class", "flow-box")
                .attr("x", 50)
                .attr("y", 50)
                .attr("width", 120)
                .attr("height", 60);
            
            svg.append("text")
                .attr("class", "text-label")
                .attr("x", 110)
                .attr("y", 75)
                .text("Input Text");
            
            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 110)
                .attr("y", 90)
                .text("Tokenization");
            
            // BERT Model
            svg.append("rect")
                .attr("class", "model-box")
                .attr("x", 250)
                .attr("y", 20)
                .attr("width", 120)
                .attr("height", 60);
            
            svg.append("text")
                .attr("class", "text-label")
                .attr("x", 310)
                .attr("y", 45)
                .text("BERT Model");
            
            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 310)
                .attr("y", 60)
                .text("Fluency Assessment");
            
            // T5 Model
            svg.append("rect")
                .attr("class", "model-box")
                .attr("x", 250)
                .attr("y", 120)
                .attr("width", 120)
                .attr("height", 60);
            
            svg.append("text")
                .attr("class", "text-label")
                .attr("x", 310)
                .attr("y", 145)
                .text("T5 Model");
            
            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 310)
                .attr("y", 160)
                .text("Correctness Assessment");
            
            // Score Integration
            svg.append("rect")
                .attr("class", "flow-box")
                .attr("x", 450)
                .attr("y", 70)
                .attr("width", 120)
                .attr("height", 60);
            
            svg.append("text")
                .attr("class", "text-label")
                .attr("x", 510)
                .attr("y", 90)
                .text("Score Integration");
            
            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 510)
                .attr("y", 105)
                .text("Final_Score = w₁F + w₂C");
            
            // Final Output
            svg.append("rect")
                .attr("class", "output-box")
                .attr("x", 650)
                .attr("y", 70)
                .attr("width", 120)
                .attr("height", 60);
            
            svg.append("text")
                .attr("class", "text-label")
                .attr("x", 710)
                .attr("y", 90)
                .text("Final Assessment");
            
            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 710)
                .attr("y", 105)
                .text("Score + Feedback");
            
            // Arrows
            svg.append("line")
                .attr("class", "arrow")
                .attr("x1", 170)
                .attr("y1", 70)
                .attr("x2", 230)
                .attr("y2", 50);
            
            svg.append("line")
                .attr("class", "arrow")
                .attr("x1", 170)
                .attr("y1", 90)
                .attr("x2", 230)
                .attr("y2", 150);
            
            svg.append("line")
                .attr("class", "arrow")
                .attr("x1", 370)
                .attr("y1", 50)
                .attr("x2", 430)
                .attr("y2", 85);
            
            svg.append("line")
                .attr("class", "arrow")
                .attr("x1", 370)
                .attr("y1", 150)
                .attr("x2", 430)
                .attr("y2", 115);
            
            svg.append("line")
                .attr("class", "arrow")
                .attr("x1", 570)
                .attr("y1", 100)
                .attr("x2", 630)
                .attr("y2", 100);
            
            // Add score annotations
            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 400)
                .attr("y", 40)
                .text("Fluency_Score = σ(BERT_output)");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 400)
                .attr("y", 140)
                .text("Correctness_Score = 100 × e^(-2.5 × Error_Ratio)");
        }
        
        // Figure 2: BERT Architecture
        function createBERTArchitecture() {
            const svg = d3.select("#bert-architecture");
            const width = 800;
            const height = 500;
            
            // Layer positions
            const layers = [
                {name: "Input", y: 50, neurons: 5, color: "#e8f4f8"},
                {name: "Embedding", y: 120, neurons: 8, color: "#fff3cd"},
                {name: "BERT Layers", y: 190, neurons: 12, color: "#ffeaa7"},
                {name: "Pooling", y: 260, neurons: 6, color: "#fab1a0"},
                {name: "Dense (256)", y: 330, neurons: 4, color: "#fd79a8"},
                {name: "Dense (128)", y: 400, neurons: 3, color: "#fdcb6e"},
                {name: "Output", y: 470, neurons: 1, color: "#00b894"}
            ];
            
            // Draw layers
            layers.forEach(layer => {
                const startX = 400 - (layer.neurons * 25) / 2;
                
                for (let i = 0; i < layer.neurons; i++) {
                    svg.append("circle")
                        .attr("class", "neuron")
                        .attr("cx", startX + i * 25)
                        .attr("cy", layer.y)
                        .attr("r", 8)
                        .style("fill", layer.color);
                }
                
                // Layer labels
                svg.append("text")
                    .attr("class", "layer-label")
                    .attr("x", 50)
                    .attr("y", layer.y + 5)
                    .text(layer.name);
            });
            
            // Draw connections between layers
            for (let i = 0; i < layers.length - 1; i++) {
                const currentLayer = layers[i];
                const nextLayer = layers[i + 1];
                
                const currentStartX = 400 - (currentLayer.neurons * 25) / 2;
                const nextStartX = 400 - (nextLayer.neurons * 25) / 2;
                
                for (let j = 0; j < currentLayer.neurons; j++) {
                    for (let k = 0; k < nextLayer.neurons; k++) {
                        svg.append("line")
                            .attr("class", "connection")
                            .attr("x1", currentStartX + j * 25)
                            .attr("y1", currentLayer.y + 8)
                            .attr("x2", nextStartX + k * 25)
                            .attr("y2", nextLayer.y - 8);
                    }
                }
            }
            
            // Add architecture details
            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 600)
                .attr("y", 100)
                .text("BERT-base-uncased");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 600)
                .attr("y", 120)
                .text("110M parameters");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 600)
                .attr("y", 140)
                .text("12 layers, 768 hidden");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 600)
                .attr("y", 160)
                .text("12 attention heads");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 600)
                .attr("y", 180)
                .text("Regression: 768→512→256→128→1");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 600)
                .attr("y", 200)
                .text("Dropout: 0.3, 0.2, 0.1");
        }
        
        // Training Progress Chart
        function createTrainingProgress() {
            const svg = d3.select("#training-progress");
            const margin = {top: 20, right: 80, bottom: 50, left: 60};
            const width = 800 - margin.left - margin.right;
            const height = 300 - margin.top - margin.bottom;
            
            const data = [
                {epoch: 0.2, trainLoss: 0.8234, valLoss: 0.8156, trainAcc: 0.456, valAcc: 0.442},
                {epoch: 0.5, trainLoss: 0.6931, valLoss: 0.6925, trainAcc: 0.620, valAcc: 0.618},
                {epoch: 1, trainLoss: 0.4523, valLoss: 0.4687, trainAcc: 0.785, valAcc: 0.773},
                {epoch: 1.5, trainLoss: 0.3234, valLoss: 0.3456, trainAcc: 0.856, valAcc: 0.834},
                {epoch: 2, trainLoss: 0.2456, valLoss: 0.2687, trainAcc: 0.912, valAcc: 0.887},
                {epoch: 2.5, trainLoss: 0.1987, valLoss: 0.2234, trainAcc: 0.934, valAcc: 0.918},
                {epoch: 3, trainLoss: 0.1756, valLoss: 0.2156, trainAcc: 0.944, valAcc: 0.927}
            ];
            
            const g = svg.append("g")
                .attr("transform", `translate(${margin.left},${margin.top})`);
            
            // Scales
            const xScale = d3.scaleLinear()
                .domain([0, 3])
                .range([0, width]);
            
            const yScale = d3.scaleLinear()
                .domain([0, 1])
                .range([height, 0]);
            
            // Line generators
            const trainAccLine = d3.line()
                .x(d => xScale(d.epoch))
                .y(d => yScale(d.trainAcc));
            
            const valAccLine = d3.line()
                .x(d => xScale(d.epoch))
                .y(d => yScale(d.valAcc));
            
            // Add lines
            g.append("path")
                .datum(data)
                .attr("fill", "none")
                .attr("stroke", "#2c3e50")
                .attr("stroke-width", 2)
                .attr("d", trainAccLine);
            
            g.append("path")
                .datum(data)
                .attr("fill", "none")
                .attr("stroke", "#e74c3c")
                .attr("stroke-width", 2)
                .attr("stroke-dasharray", "5,5")
                .attr("d", valAccLine);
            
            // Add axes
            g.append("g")
                .attr("transform", `translate(0,${height})`)
                .call(d3.axisBottom(xScale))
                .append("text")
                .attr("class", "axis-label")
                .attr("x", width / 2)
                .attr("y", 40)
                .text("Training Epoch");
            
            g.append("g")
                .call(d3.axisLeft(yScale))
                .append("text")
                .attr("class", "axis-label")
                .attr("transform", "rotate(-90)")
                .attr("y", -40)
                .attr("x", -height / 2)
                .text("Accuracy");
            
            // Legend
            const legend = g.append("g")
                .attr("transform", `translate(${width - 100}, 20)`);
            
            legend.append("line")
                .attr("x1", 0)
                .attr("x2", 20)
                .attr("y1", 0)
                .attr("y2", 0)
                .style("stroke", "#2c3e50")
                .style("stroke-width", 2);
            
            legend.append("text")
                .attr("x", 25)
                .attr("y", 5)
                .style("font-size", "12px")
                .text("Train Acc");
            
            legend.append("line")
                .attr("x1", 0)
                .attr("x2", 20)
                .attr("y1", 20)
                .attr("y2", 20)
                .style("stroke", "#e74c3c")
                .style("stroke-width", 2)
                .style("stroke-dasharray", "5,5");
            
            legend.append("text")
                .attr("x", 25)
                .attr("y", 25)
                .style("font-size", "12px")
                .text("Val Acc");
        }
        
        // T5 Workflow
        function createT5Workflow() {
            const svg = d3.select("#t5-workflow");
            const width = 800;
            const height = 400;
            
            createArrowMarkers(svg);
            
            // Input
            svg.append("rect")
                .attr("class", "flow-box")
                .attr("x", 50)
                .attr("y", 50)
                .attr("width", 100)
                .attr("height", 50);
            
            svg.append("text")
                .attr("class", "text-label")
                .attr("x", 100)
                .attr("y", 75)
                .text("Input Text");
            
            // T5 Processing
            svg.append("rect")
                .attr("class", "model-box")
                .attr("x", 200)
                .attr("y", 50)
                .attr("width", 120)
                .attr("height", 50);
            
            svg.append("text")
                .attr("class", "text-label")
                .attr("x", 260)
                .attr("y", 70)
                .text("T5 Model");
            
            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 260)
                .attr("y", 85)
                .text("FLAN-T5-large");
            
            // Error Detection
            svg.append("rect")
                .attr("class", "flow-box")
                .attr("x", 370)
                .attr("y", 50)
                .attr("width", 120)
                .attr("height", 50);
            
            svg.append("text")
                .attr("class", "text-label")
                .attr("x", 430)
                .attr("y", 70)
                .text("Error Detection");
            
            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 430)
                .attr("y", 85)
                .text("Input vs Output");
            
            // Error Classification
            svg.append("rect")
                .attr("class", "flow-box")
                .attr("x", 200)
                .attr("y", 150)
                .attr("width", 120)
                .attr("height", 50);
            
            svg.append("text")
                .attr("class", "text-label")
                .attr("x", 260)
                .attr("y", 170)
                .text("Error Classification");
            
            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 260)
                .attr("y", 185)
                .text("Grammar, Spelling, etc.");
            
            // Severity Assessment
            svg.append("rect")
                .attr("class", "flow-box")
                .attr("x", 370)
                .attr("y", 150)
                .attr("width", 120)
                .attr("height", 50);
            
            svg.append("text")
                .attr("class", "text-label")
                .attr("x", 430)
                .attr("y", 170)
                .text("Severity Assessment");
            
            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 430)
                .attr("y", 185)
                .text("Critical/Major/Minor");
            
            // Final Score
            svg.append("rect")
                .attr("class", "output-box")
                .attr("x", 540)
                .attr("y", 100)
                .attr("width", 120)
                .attr("height", 50);
            
            svg.append("text")
                .attr("class", "text-label")
                .attr("x", 600)
                .attr("y", 120)
                .text("Correctness Score");
            
            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 600)
                .attr("y", 135)
                .text("100 × e^(-α × Error_Ratio)");
            
            // Arrows
            svg.append("line")
                .attr("class", "arrow")
                .attr("x1", 150)
                .attr("y1", 75)
                .attr("x2", 190)
                .attr("y2", 75);
            
            svg.append("line")
                .attr("class", "arrow")
                .attr("x1", 320)
                .attr("y1", 75)
                .attr("x2", 360)
                .attr("y2", 75);
            
            svg.append("line")
                .attr("class", "arrow")
                .attr("x1", 430)
                .attr("y1", 100)
                .attr("x2", 280)
                .attr("y2", 140);
            
            svg.append("line")
                .attr("class", "arrow")
                .attr("x1", 320)
                .attr("y1", 175)
                .attr("x2", 360)
                .attr("y2", 175);
            
            svg.append("line")
                .attr("class", "arrow")
                .attr("x1", 490)
                .attr("y1", 160)
                .attr("x2", 530)
                .attr("y2", 135);
            
            // Add scoring annotations
            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 50)
                .attr("y", 250)
                .text("Scoring Parameters:");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 50)
                .attr("y", 270)
                .text("α = 2.5 (penalty parameter)");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 50)
                .attr("y", 290)
                .text("Error_Ratio = Corrections / Total_Tokens");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 50)
                .attr("y", 310)
                .text("Exponential decay function");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 50)
                .attr("y", 330)
                .text("Transfer Learning: pszemraj/flan-t5-large-grammar-synthesis");
        }
        
        // Error Distribution Chart
        function createErrorDistribution() {
            const svg = d3.select("#error-distribution");
            const margin = {top: 20, right: 20, bottom: 50, left: 60};
            const width = 400 - margin.left - margin.right;
            const height = 300 - margin.top - margin.bottom;
            
            const data = [
                {type: "Grammatical", count: 3456, percentage: 38.4},
                {type: "Mechanical", count: 2834, percentage: 31.5},
                {type: "Syntactic", count: 1967, percentage: 21.9},
                {type: "Morphological", count: 743, percentage: 8.2}
            ];
            
            const g = svg.append("g")
                .attr("transform", `translate(${margin.left},${margin.top})`);
            
            const xScale = d3.scaleBand()
                .domain(data.map(d => d.type))
                .range([0, width])
                .padding(0.1);
            
            const yScale = d3.scaleLinear()
                .domain([0, d3.max(data, d => d.count)])
                .range([height, 0]);
            
            g.selectAll(".bar")
                .data(data)
                .enter().append("rect")
                .attr("class", "bar")
                .attr("x", d => xScale(d.type))
                .attr("width", xScale.bandwidth())
                .attr("y", d => yScale(d.count))
                .attr("height", d => height - yScale(d.count));
            
            // Add percentage labels
            g.selectAll(".label")
                .data(data)
                .enter().append("text")
                .attr("class", "small-text")
                .attr("x", d => xScale(d.type) + xScale.bandwidth() / 2)
                .attr("y", d => yScale(d.count) - 5)
                .attr("text-anchor", "middle")
                .text(d => d.percentage + "%");
            
            // Axes
            g.append("g")
                .attr("transform", `translate(0,${height})`)
                .call(d3.axisBottom(xScale))
                .selectAll("text")
                .style("text-anchor", "end")
                .attr("dx", "-.8em")
                .attr("dy", ".15em")
                .attr("transform", "rotate(-45)");
            
            g.append("g")
                .call(d3.axisLeft(yScale));
            
            g.append("text")
                .attr("class", "axis-label")
                .attr("transform", "rotate(-90)")
                .attr("y", -40)
                .attr("x", -height / 2)
                .text("Error Count");
        }
        
        // Model Comparison Chart
        function createModelComparison() {
            const svg = d3.select("#model-comparison");
            const margin = {top: 20, right: 100, bottom: 50, left: 60};
            const width = 800 - margin.left - margin.right;
            const height = 300 - margin.top - margin.bottom;
            
            const data = [
                {metric: "Accuracy", bert: 0.927, t5: 0.879},
                {metric: "Precision", bert: 0.934, t5: 0.891},
                {metric: "Recall", bert: 0.921, t5: 0.867},
                {metric: "F1-Score", bert: 0.927, t5: 0.879}
            ];
            
            const g = svg.append("g")
                .attr("transform", `translate(${margin.left},${margin.top})`);
            
            const xScale = d3.scaleBand()
                .domain(data.map(d => d.metric))
                .range([0, width])
                .padding(0.1);
            
            const yScale = d3.scaleLinear()
                .domain([0, 1])
                .range([height, 0]);
            
            const bertBars = g.selectAll(".bert-bar")
                .data(data)
                .enter().append("rect")
                .attr("class", "bert-bar")
                .attr("x", d => xScale(d.metric))
                .attr("width", xScale.bandwidth() / 2)
                .attr("y", d => yScale(d.bert))
                .attr("height", d => height - yScale(d.bert))
                .attr("fill", "#3498db");
            
            const t5Bars = g.selectAll(".t5-bar")
                .data(data)
                .enter().append("rect")
                .attr("class", "t5-bar")
                .attr("x", d => xScale(d.metric) + xScale.bandwidth() / 2)
                .attr("width", xScale.bandwidth() / 2)
                .attr("y", d => yScale(d.t5))
                .attr("height", d => height - yScale(d.t5))
                .attr("fill", "#e74c3c");
            
            // Add value labels
            g.selectAll(".bert-label")
                .data(data)
                .enter().append("text")
                .attr("class", "small-text")
                .attr("x", d => xScale(d.metric) + xScale.bandwidth() / 4)
                .attr("y", d => yScale(d.bert) - 5)
                .attr("text-anchor", "middle")
                .text(d => d.bert.toFixed(3));
            
            g.selectAll(".t5-label")
                .data(data)
                .enter().append("text")
                .attr("class", "small-text")
                .attr("x", d => xScale(d.metric) + 3 * xScale.bandwidth() / 4)
                .attr("y", d => yScale(d.t5) - 5)
                .attr("text-anchor", "middle")
                .text(d => d.t5.toFixed(3));
            
            // Axes
            g.append("g")
                .attr("transform", `translate(0,${height})`)
                .call(d3.axisBottom(xScale));
            
            g.append("g")
                .call(d3.axisLeft(yScale));
            
            g.append("text")
                .attr("class", "axis-label")
                .attr("transform", "rotate(-90)")
                .attr("y", -40)
                .attr("x", -height / 2)
                .text("Performance Score");
            
            // Legend
            const legend = g.append("g")
                .attr("transform", `translate(${width - 80}, 20)`);
            
            legend.append("rect")
                .attr("width", 15)
                .attr("height", 15)
                .attr("fill", "#3498db");
            
            legend.append("text")
                .attr("x", 20)
                .attr("y", 12)
                .style("font-size", "12px")
                .text("BERT");
            
            legend.append("rect")
                .attr("y", 20)
                .attr("width", 15)
                .attr("height", 15)
                .attr("fill", "#e74c3c");
            
            legend.append("text")
                .attr("x", 20)
                .attr("y", 32)
                .style("font-size", "12px")
                .text("T5");
        }
        
        // Score Distribution Charts
        function createScoreDistribution(svgId, data, title) {
            const svg = d3.select(svgId);
            const margin = {top: 20, right: 20, bottom: 50, left: 60};
            const width = 400 - margin.left - margin.right;
            const height = 300 - margin.top - margin.bottom;
            
            const g = svg.append("g")
                .attr("transform", `translate(${margin.left},${margin.top})`);
            
            const xScale = d3.scaleBand()
                .domain(data.map(d => d.range))
                .range([0, width])
                .padding(0.1);
            
            const yScale = d3.scaleLinear()
                .domain([0, d3.max(data, d => d.frequency)])
                .range([height, 0]);
            
            g.selectAll(".bar")
                .data(data)
                .enter().append("rect")
                .attr("class", "bar")
                .attr("x", d => xScale(d.range))
                .attr("width", xScale.bandwidth())
                .attr("y", d => yScale(d.frequency))
                .attr("height", d => height - yScale(d.frequency));
            
            // Add percentage labels
            g.selectAll(".label")
                .data(data)
                .enter().append("text")
                .attr("class", "small-text")
                .attr("x", d => xScale(d.range) + xScale.bandwidth() / 2)
                .attr("y", d => yScale(d.frequency) - 5)
                .attr("text-anchor", "middle")
                .text(d => d.percentage + "%");
            
            // Axes
            g.append("g")
                .attr("transform", `translate(0,${height})`)
                .call(d3.axisBottom(xScale));
            
            g.append("g")
                .call(d3.axisLeft(yScale));
            
            g.append("text")
                .attr("class", "axis-label")
                .attr("transform", "rotate(-90)")
                .attr("y", -40)
                .attr("x", -height / 2)
                .text("Frequency");
        }
        
        // Weight Optimization Chart
        function createWeightOptimization() {
            const svg = d3.select("#weight-optimization");
            const margin = {top: 20, right: 20, bottom: 50, left: 60};
            const width = 800 - margin.left - margin.right;
            const height = 300 - margin.top - margin.bottom;
            
            const data = [
                {config: "0.3/0.7", fluency: 0.3, correctness: 0.7, correlation: 0.867, rmse: 0.241},
                {config: "0.4/0.6", fluency: 0.4, correctness: 0.6, correlation: 0.883, rmse: 0.225},
                {config: "0.5/0.5", fluency: 0.5, correctness: 0.5, correlation: 0.876, rmse: 0.234},
                {config: "0.6/0.4", fluency: 0.6, correctness: 0.4, correlation: 0.891, rmse: 0.218},
                {config: "0.7/0.3", fluency: 0.7, correctness: 0.3, correlation: 0.854, rmse: 0.258}
            ];
            
            const g = svg.append("g")
                .attr("transform", `translate(${margin.left},${margin.top})`);
            
            const xScale = d3.scaleBand()
                .domain(data.map(d => d.config))
                .range([0, width])
                .padding(0.1);
            
            const yScale = d3.scaleLinear()
                .domain([0.84, 0.9])
                .range([height, 0]);
            
            // Bars for correlation
            g.selectAll(".correlation-bar")
                .data(data)
                .enter().append("rect")
                .attr("class", "correlation-bar")
                .attr("x", d => xScale(d.config))
                .attr("width", xScale.bandwidth())
                .attr("y", d => yScale(d.correlation))
                .attr("height", d => height - yScale(d.correlation))
                .attr("fill", d => d.config === "0.6/0.4" ? "#27ae60" : "#3498db");
            
            // Add correlation values
            g.selectAll(".correlation-label")
                .data(data)
                .enter().append("text")
                .attr("class", "small-text")
                .attr("x", d => xScale(d.config) + xScale.bandwidth() / 2)
                .attr("y", d => yScale(d.correlation) - 5)
                .attr("text-anchor", "middle")
                .text(d => d.correlation.toFixed(3));
            
            // Highlight optimal configuration
            g.append("rect")
                .attr("x", xScale("0.6/0.4") - 2)
                .attr("y", yScale(0.891) - 2)
                .attr("width", xScale.bandwidth() + 4)
                .attr("height", height - yScale(0.891) + 4)
                .attr("fill", "none")
                .attr("stroke", "#e74c3c")
                .attr("stroke-width", 3)
                .attr("stroke-dasharray", "5,5");
            
            // Axes
            g.append("g")
                .attr("transform", `translate(0,${height})`)
                .call(d3.axisBottom(xScale));
            
            g.append("g")
                .call(d3.axisLeft(yScale));
            
            g.append("text")
                .attr("class", "axis-label")
                .attr("x", width / 2)
                .attr("y", height + 40)
                .attr("text-anchor", "middle")
                .text("Weight Configuration (w₁/w₂)");
            
            g.append("text")
                .attr("class", "axis-label")
                .attr("transform", "rotate(-90)")
                .attr("y", -40)
                .attr("x", -height / 2)
                .text("Correlation with Human Judgments");
        }

        // Fluency Distribution Chart
        function createFluencyDistribution() {
            const svg = d3.select("#fluency-distribution");
            const margin = {top: 20, right: 20, bottom: 50, left: 60};
            const width = 400 - margin.left - margin.right;
            const height = 300 - margin.top - margin.bottom;

            const data = [
                {range: "5.0-5.9", frequency: 8, percentage: 8.9},
                {range: "6.0-6.9", frequency: 15, percentage: 16.7},
                {range: "7.0-7.9", frequency: 32, percentage: 35.6},
                {range: "8.0-8.9", frequency: 24, percentage: 26.7},
                {range: "9.0-10.0", frequency: 11, percentage: 12.2}
            ];

            const g = svg.append("g")
                .attr("transform", `translate(${margin.left},${margin.top})`);

            const xScale = d3.scaleBand()
                .domain(data.map(d => d.range))
                .range([0, width])
                .padding(0.1);

            const yScale = d3.scaleLinear()
                .domain([0, d3.max(data, d => d.frequency)])
                .range([height, 0]);

            g.selectAll(".bar")
                .data(data)
                .enter().append("rect")
                .attr("class", "bar")
                .attr("x", d => xScale(d.range))
                .attr("width", xScale.bandwidth())
                .attr("y", d => yScale(d.frequency))
                .attr("height", d => height - yScale(d.frequency))
                .attr("fill", "#3498db");

            // Add percentage labels
            g.selectAll(".label")
                .data(data)
                .enter().append("text")
                .attr("class", "small-text")
                .attr("x", d => xScale(d.range) + xScale.bandwidth() / 2)
                .attr("y", d => yScale(d.frequency) - 5)
                .attr("text-anchor", "middle")
                .text(d => d.percentage + "%");

            // Axes
            g.append("g")
                .attr("transform", `translate(0,${height})`)
                .call(d3.axisBottom(xScale));

            g.append("g")
                .call(d3.axisLeft(yScale));

            g.append("text")
                .attr("class", "axis-label")
                .attr("transform", "rotate(-90)")
                .attr("y", -40)
                .attr("x", -height / 2)
                .text("Frequency");
        }

        // Tab switching function
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to selected tab
            event.target.classList.add('active');
        }

        // Experimental Design Diagram
        function createExperimentalDesign() {
            const svg = d3.select("#experimental-design");
            const width = 800;
            const height = 400;

            createArrowMarkers(svg);

            // Recruitment Phase
            svg.append("rect")
                .attr("class", "flow-box")
                .attr("x", 50)
                .attr("y", 50)
                .attr("width", 120)
                .attr("height", 60);

            svg.append("text")
                .attr("class", "text-label")
                .attr("x", 110)
                .attr("y", 75)
                .text("Recruitment");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 110)
                .attr("y", 90)
                .text("90 Participants");

            // Randomization
            svg.append("rect")
                .attr("class", "flow-box")
                .attr("x", 220)
                .attr("y", 50)
                .attr("width", 120)
                .attr("height", 60);

            svg.append("text")
                .attr("class", "text-label")
                .attr("x", 280)
                .attr("y", 75)
                .text("Randomization");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 280)
                .attr("y", 90)
                .text("Balanced Groups");

            // Control Group
            svg.append("rect")
                .attr("class", "model-box")
                .attr("x", 150)
                .attr("y", 150)
                .attr("width", 100)
                .attr("height", 60);

            svg.append("text")
                .attr("class", "text-label")
                .attr("x", 200)
                .attr("y", 175)
                .text("Control Group");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 200)
                .attr("y", 190)
                .text("n=45");

            // Experimental Group
            svg.append("rect")
                .attr("class", "model-box")
                .attr("x", 290)
                .attr("y", 150)
                .attr("width", 100)
                .attr("height", 60);

            svg.append("text")
                .attr("class", "text-label")
                .attr("x", 340)
                .attr("y", 175)
                .text("Experimental");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 340)
                .attr("y", 190)
                .text("n=45");

            // Assessment Phase
            svg.append("rect")
                .attr("class", "output-box")
                .attr("x", 450)
                .attr("y", 100)
                .attr("width", 120)
                .attr("height", 60);

            svg.append("text")
                .attr("class", "text-label")
                .attr("x", 510)
                .attr("y", 125)
                .text("Assessment");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 510)
                .attr("y", 140)
                .text("Human + AI");

            // Arrows
            svg.append("line")
                .attr("class", "arrow")
                .attr("x1", 170)
                .attr("y1", 80)
                .attr("x2", 210)
                .attr("y2", 80);

            svg.append("line")
                .attr("class", "arrow")
                .attr("x1", 280)
                .attr("y1", 110)
                .attr("x2", 220)
                .attr("y2", 140);

            svg.append("line")
                .attr("class", "arrow")
                .attr("x1", 280)
                .attr("y1", 110)
                .attr("x2", 320)
                .attr("y2", 140);

            svg.append("line")
                .attr("class", "arrow")
                .attr("x1", 250)
                .attr("y1", 180)
                .attr("x2", 430)
                .attr("y2", 130);

            svg.append("line")
                .attr("class", "arrow")
                .attr("x1", 390)
                .attr("y1", 180)
                .attr("x2", 430)
                .attr("y2", 130);

            // Add statistical annotations
            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 50)
                .attr("y", 280)
                .text("Pre-test Fluency: 6.85±1.13 (Control), 6.92±1.44 (Experimental)");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 50)
                .attr("y", 300)
                .text("Pre-test Correctness: 6.73±1.08 (Control), 6.81±1.39 (Experimental)");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 50)
                .attr("y", 320)
                .text("Inter-rater Reliability: α=0.847 (Fluency), α=0.823 (Correctness)");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 50)
                .attr("y", 340)
                .text("Writing Tasks: Academic activities (300-400 words, 45 min)");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 50)
                .attr("y", 360)
                .text("Age Range: 18-21 years (mean: 19.3), Economics students");

            svg.append("text")
                .attr("class", "small-text")
                .attr("x", 50)
                .attr("y", 380)
                .text("Dataset: 80% training (72 compositions), 20% testing (18 compositions)");
        }

        // Initialize all charts
        document.addEventListener('DOMContentLoaded', function() {
            createSystemOverview();
            createBERTArchitecture();
            createTrainingProgress();
            createT5Workflow();
            createErrorDistribution();
            createFluencyDistribution();
            createExperimentalDesign();
            createWeightOptimization();

            // Update T5 workflow diagram
            function drawT5Workflow() {
                const svg = d3.select('#t5-workflow');
                const width = 800;
                const height = 400;

                svg.attr('width', width).attr('height', height);
                svg.selectAll('*').remove();

                // Add workflow components
                const components = [
                    {x: 50, y: 50, width: 120, height: 60, text: 'Input Text\nPreprocessing', color: '#e3f2fd'},
                    {x: 200, y: 50, width: 120, height: 60, text: 'T5 Encoder\nContextual Rep.', color: '#f3e5f5'},
                    {x: 350, y: 50, width: 120, height: 60, text: 'T5 Decoder\nError Correction', color: '#e8f5e8'},
                    {x: 500, y: 50, width: 120, height: 60, text: 'Differential\nAnalysis', color: '#fff3e0'},
                    {x: 650, y: 50, width: 120, height: 60, text: 'Score Calculation\nS_c = 100×e^(-2.5×ER)', color: '#fce4ec'}
                ];

                // Draw components
                components.forEach(comp => {
                    svg.append('rect')
                        .attr('x', comp.x)
                        .attr('y', comp.y)
                        .attr('width', comp.width)
                        .attr('height', comp.height)
                        .attr('fill', comp.color)
                        .attr('stroke', '#333')
                        .attr('stroke-width', 1)
                        .attr('rx', 5);

                    svg.append('text')
                        .attr('x', comp.x + comp.width/2)
                        .attr('y', comp.y + comp.height/2)
                        .attr('text-anchor', 'middle')
                        .attr('dominant-baseline', 'middle')
                        .style('font-size', '12px')
                        .style('font-weight', 'bold')
                        .selectAll('tspan')
                        .data(comp.text.split('\n'))
                        .enter()
                        .append('tspan')
                        .attr('x', comp.x + comp.width/2)
                        .attr('dy', (d, i) => i === 0 ? 0 : 15)
                        .text(d => d);
                });

                // Add arrowhead marker
                svg.append('defs').append('marker')
                    .attr('id', 'arrowhead')
                    .attr('viewBox', '0 -5 10 10')
                    .attr('refX', 8)
                    .attr('refY', 0)
                    .attr('markerWidth', 6)
                    .attr('markerHeight', 6)
                    .attr('orient', 'auto')
                    .append('path')
                    .attr('d', 'M0,-5L10,0L0,5')
                    .attr('fill', '#333');

                // Draw arrows
                const arrows = [
                    {x1: 170, y1: 80, x2: 200, y2: 80},
                    {x1: 320, y1: 80, x2: 350, y2: 80},
                    {x1: 470, y1: 80, x2: 500, y2: 80},
                    {x1: 620, y1: 80, x2: 650, y2: 80}
                ];

                arrows.forEach(arrow => {
                    svg.append('line')
                        .attr('x1', arrow.x1)
                        .attr('y1', arrow.y1)
                        .attr('x2', arrow.x2)
                        .attr('y2', arrow.y2)
                        .attr('stroke', '#333')
                        .attr('stroke-width', 2)
                        .attr('marker-end', 'url(#arrowhead)');
                });
            }

            // Call new drawing functions
            drawT5Workflow();
        });
    </script>
</body>
</html>