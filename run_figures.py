#!/usr/bin/env python3
"""
Run script for generating all dual-model figures
Usage: python run_figures.py
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from dual_model_figures import DualModelFigures
    import matplotlib.pyplot as plt
    
    def generate_individual_figure(figure_num):
        """Generate a specific figure by number"""
        generator = DualModelFigures()
        
        figures = {
            1: generator.figure1_system_architecture,
            2: generator.figure2_bert_architecture,
            3: generator.figure3_t5_workflow,
            4: generator.figure4_training_progress,
            5: generator.figure5_experimental_design,
            6: generator.figure6_performance_analysis,
            7: generator.figure7_system_integration
        }
        
        if figure_num in figures:
            print(f"Generating Figure {figure_num}...")
            fig = figures[figure_num]()
            plt.show()
            return fig
        else:
            print(f"Figure {figure_num} not found. Available figures: 1-7")
            return None
    
    def main():
        if len(sys.argv) > 1:
            try:
                fig_num = int(sys.argv[1])
                generate_individual_figure(fig_num)
            except ValueError:
                print("Please provide a valid figure number (1-7)")
        else:
            # Generate all figures
            generator = DualModelFigures()
            generator.generate_all_figures()

            print("All figures generated successfully!")
            print("No tables in images - all statistical data is in methodology text.")
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Please install required packages: pip install -r requirements.txt")
except Exception as e:
    print(f"Error generating figures: {e}")
    import traceback
    traceback.print_exc()
