import asyncio
from playwright.async_api import async_playwright
import os

async def capture_figures():
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        page = await browser.new_page()

        # Set high resolution viewport for better image quality
        await page.set_viewport_size({"width": 1920, "height": 1080})

        # Load the HTML file
        file_path = os.path.abspath("dual_model_figures.html")
        await page.goto(f"file://{file_path}")

        # Wait for D3 charts to load
        await page.wait_for_timeout(5000)
        
        # Define the figures to capture
        figures = [
            {"tab": "overview", "selector": "#system-overview", "name": "image_one_system_architecture"},
            {"tab": "bert", "selector": "#bert-architecture", "name": "image_two_bert_architecture"},
            {"tab": "bert", "selector": "#training-progress", "name": "image_three_training_progress"},
            {"tab": "t5", "selector": "#t5-workflow", "name": "image_four_t5_workflow"},
            {"tab": "t5", "selector": "#error-distribution", "name": "image_five_error_distribution"},
            {"tab": "t5", "selector": "#fluency-distribution", "name": "image_six_fluency_distribution", "fallback": True},
            {"tab": "performance", "selector": "#experimental-design", "name": "image_seven_experimental_design"},
            {"tab": "validation", "selector": "#weight-optimization", "name": "image_eight_weight_optimization"}
        ]
        
        for i, figure in enumerate(figures, 1):
            try:
                # Click the appropriate tab
                await page.click(f'button[onclick="showTab(\'{figure["tab"]}\')"]')
                await page.wait_for_timeout(1000)
                
                # Wait for the SVG to be visible
                await page.wait_for_selector(figure["selector"], state="visible")
                
                # Take high-resolution screenshot of the specific element
                element = await page.query_selector(figure["selector"])
                if element:
                    # Get element dimensions for optimal sizing
                    box = await element.bounding_box()
                    if box and box['width'] > 0 and box['height'] > 0:
                        try:
                            # Use page screenshot with clip for higher quality
                            await page.screenshot(
                                path=f"{figure['name']}.png",
                                type='png',
                                clip={
                                    'x': box['x'],
                                    'y': box['y'],
                                    'width': box['width'],
                                    'height': box['height']
                                },
                                full_page=False
                            )
                            print(f"Captured high-resolution {figure['name']}.png ({int(box['width'])}x{int(box['height'])})")
                        except Exception as e:
                            # Fallback to element screenshot
                            await element.screenshot(path=f"{figure['name']}.png")
                            print(f"Captured {figure['name']}.png (fallback method)")
                    else:
                        await element.screenshot(path=f"{figure['name']}.png")
                        print(f"Captured {figure['name']}.png")
                else:
                    print(f"Could not find element {figure['selector']}")
                    
            except Exception as e:
                print(f"Error capturing {figure['name']}: {e}")
        
        await browser.close()

if __name__ == "__main__":
    asyncio.run(capture_figures())
