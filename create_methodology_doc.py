#!/usr/bin/env python3
"""
Script to convert methodology.txt to a properly formatted Word document
with APA 7th edition tables and high-resolution images.
"""

from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_BREAK
from docx.enum.table import WD_TABLE_ALIGNMENT, WD_ALIGN_VERTICAL
from docx.oxml.shared import OxmlElement, qn
from docx.oxml.ns import nsdecls
from docx.oxml import parse_xml
import os
from PIL import Image

def set_table_borders(table):
    """Set full grid borders for tables (all borders visible)"""
    tbl = table._tbl
    tblBorders = OxmlElement('w:tblBorders')

    # All borders with same style
    border_style = {
        qn('w:val'): 'single',
        qn('w:sz'): '4',  # 0.5pt for thin lines
        qn('w:space'): '0',
        qn('w:color'): '000000'
    }

    # Top border
    top_border = OxmlElement('w:top')
    for attr, value in border_style.items():
        top_border.set(attr, value)
    tblBorders.append(top_border)

    # Bottom border
    bottom_border = OxmlElement('w:bottom')
    for attr, value in border_style.items():
        bottom_border.set(attr, value)
    tblBorders.append(bottom_border)

    # Left border
    left_border = OxmlElement('w:left')
    for attr, value in border_style.items():
        left_border.set(attr, value)
    tblBorders.append(left_border)

    # Right border
    right_border = OxmlElement('w:right')
    for attr, value in border_style.items():
        right_border.set(attr, value)
    tblBorders.append(right_border)

    # Inside horizontal borders
    inside_h = OxmlElement('w:insideH')
    for attr, value in border_style.items():
        inside_h.set(attr, value)
    tblBorders.append(inside_h)

    # Inside vertical borders
    inside_v = OxmlElement('w:insideV')
    for attr, value in border_style.items():
        inside_v.set(attr, value)
    tblBorders.append(inside_v)

    tbl.tblPr.append(tblBorders)

def create_apa_table(doc, title, headers, data, note=None):
    """Create a table with full grid borders matching the user's format"""
    # Add table title
    title_para = doc.add_paragraph()
    title_run = title_para.add_run(title)
    title_run.bold = True
    title_run.italic = True
    title_para.alignment = WD_ALIGN_PARAGRAPH.LEFT

    # Create table
    table = doc.add_table(rows=1, cols=len(headers))
    table.alignment = WD_TABLE_ALIGNMENT.LEFT
    table.style = 'Table Grid'

    # Set table borders
    set_table_borders(table)

    # Add headers with proper formatting
    hdr_cells = table.rows[0].cells
    for i, header in enumerate(headers):
        hdr_cells[i].text = header
        # Make header text bold
        for paragraph in hdr_cells[i].paragraphs:
            for run in paragraph.runs:
                run.bold = True
        hdr_cells[i].vertical_alignment = WD_ALIGN_VERTICAL.CENTER
        hdr_cells[i].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Add some padding to cells
        cell_margins = hdr_cells[i]._tc.tcPr
        if cell_margins is None:
            cell_margins = OxmlElement('w:tcPr')
            hdr_cells[i]._tc.append(cell_margins)

    # Add data rows
    for row_data in data:
        row_cells = table.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = str(cell_data)
            row_cells[i].vertical_alignment = WD_ALIGN_VERTICAL.CENTER
            if i == 0:  # Left align first column (metric names)
                row_cells[i].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
            else:  # Center align other columns (values)
                row_cells[i].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER

            # Add some padding to cells
            cell_margins = row_cells[i]._tc.tcPr
            if cell_margins is None:
                cell_margins = OxmlElement('w:tcPr')
                row_cells[i]._tc.append(cell_margins)

    # Add note if provided
    if note:
        note_para = doc.add_paragraph()
        note_para.add_run(note).italic = True
        note_para.alignment = WD_ALIGN_PARAGRAPH.LEFT

    # Add spacing after table
    doc.add_paragraph()

def add_image_with_caption(doc, image_path, caption, width_inches=6.0):
    """Add an image with caption to the document"""
    if os.path.exists(image_path):
        # Add the image
        paragraph = doc.add_paragraph()
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        run = paragraph.add_run()
        run.add_picture(image_path, width=Inches(width_inches))
        
        # Add caption
        caption_para = doc.add_paragraph()
        caption_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        caption_run = caption_para.add_run(caption)
        caption_run.italic = True
        caption_run.font.size = Pt(10)
        
        # Add spacing
        doc.add_paragraph()
    else:
        # Add placeholder if image not found
        placeholder_para = doc.add_paragraph()
        placeholder_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        placeholder_run = placeholder_para.add_run(f"[IMAGE PLACEHOLDER: {caption}]")
        placeholder_run.italic = True
        placeholder_run.font.color.rgb = RGBColor(128, 128, 128)
        doc.add_paragraph()

def create_methodology_document():
    """Create the main methodology document with all improvements"""
    doc = Document()

    # Set document margins and font
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1)
        section.right_margin = Inches(1)

    # Title
    title = doc.add_heading('Enhanced Methodology', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # Overview section
    doc.add_heading('Overview', level=1)
    overview_text = """This study presents a framework for evaluating English writing quality through the development and implementation of a dual-model automated writing evaluation system. The methodology follows a systematic approach that encompasses theoretical foundations, data preprocessing, model architecture design, training procedures, and evaluation metrics. This dual-model framework provides holistic assessment of writing proficiency by simultaneously analyzing both fluency and correctness dimensions of written expression, addressing the limitations of traditional single-metric evaluation approaches."""
    doc.add_paragraph(overview_text)

    # Theoretical Framework section
    doc.add_heading('Theoretical Framework and Task Definition', level=1)
    doc.add_heading('Conceptual Foundation', level=2)

    conceptual_text = """The theoretical foundation of this study rests on the principle that writing assessment requires multidimensional evaluation that captures both the expressive quality and linguistic accuracy of written discourse. Traditional automated writing evaluation systems typically focus on either grammatical correctness or stylistic fluency, creating an incomplete assessment paradigm that fails to reflect the holistic nature of writing proficiency.

Building upon established frameworks in second language writing assessment, this study proposes that effective automated evaluation must simultaneously consider two fundamental dimensions: fluency (the naturalness, coherence, and flow of expression) and correctness (grammatical accuracy, mechanical precision, and adherence to standard conventions). These dimensions are not mutually exclusive but rather complementary aspects of writing quality that together provide a complete picture of language proficiency."""
    doc.add_paragraph(conceptual_text)

    doc.add_heading('Task Definition and Scope', level=2)
    task_text = """In accordance with the requirements of automated writing assessment for English language learners, this study develops a novel dual-model computer-assisted English writing evaluation system. The system addresses the critical gap in current evaluation methodologies by providing real-time feedback on both linguistic fluency and grammatical correctness.

The proposed system evaluates compositions across two primary indicators: fluency assessment measures naturalness, coherence, vocabulary appropriateness, and overall flow of expression, while correctness evaluation identifies and quantifies grammatical errors, mechanical mistakes, and syntactic inconsistencies.

The system incorporates specialized neural network modules to conduct these evaluations, diagnoses errors across multiple linguistic dimensions (vocabulary, grammar, mechanics), provides targeted correction suggestions, and delivers itemized feedback to support systematic language learning improvement."""
    doc.add_paragraph(task_text)

    # System Architecture section
    doc.add_heading('System Architecture and Design', level=1)
    doc.add_heading('Architectural Overview', level=2)

    arch_text = """The dual-model system architecture employs a parallel processing framework where input essays undergo simultaneous analysis through two specialized evaluation pathways. This design ensures assessment while maintaining computational efficiency and evaluation consistency.

The system processes input text through the following pipeline: preprocessing module handles text normalization, tokenization, and sequence preparation; parallel processing enables simultaneous evaluation through fluency and correctness modules; score integration combines component scores through weighted averaging; feedback generation produces detailed diagnostic output and improvement suggestions."""
    doc.add_paragraph(arch_text)

    # Add Figure 1
    add_image_with_caption(doc, "image_one_system_architecture.png",
                          "Figure 1. Dual-Model System Architecture")

    return doc

def add_mathematical_formulation(doc):
    """Add mathematical formulation section"""
    doc.add_heading('Mathematical Formulation', level=2)

    math_text = """The evaluation process is formalized through the following mathematical models:

Final Score Calculation:
Final_Score = w₁ × Fluency_Score + w₂ × Correctness_Score    (1)

Fluency Score Computation:
Fluency_Score = σ(BERT_output)    (2)

Correctness Score Computation:
Correctness_Score = 100 × e^(-α × Error_Ratio)    (3)

Where w₁ and w₂ represent empirically determined weighting coefficients (w₁ = 0.6, w₂ = 0.4), σ denotes the sigmoid activation function, and α is the penalty parameter calibrated to 2.5 based on extensive validation against human judgments."""
    doc.add_paragraph(math_text)

def add_fluency_module(doc):
    """Add fluency assessment module section"""
    doc.add_heading('Fluency Assessment Module', level=1)
    doc.add_heading('Theoretical Foundations', level=2)

    fluency_theory = """Writing fluency encompasses the natural flow and rhythm of language usage, including appropriate vocabulary selection, sentence structure diversity, syntactic complexity, and overall coherence. Fluency evaluation addresses whether ideas are expressed smoothly and naturally, approximating native-like language production patterns.

Traditional fluency assessment relies heavily on subjective human evaluation, which presents significant scalability challenges, consistency issues, and inter-rater reliability concerns. The proposed system addresses these limitations by implementing a BERT-based neural network approach that automatically captures semantic coherence and linguistic naturalness through deep contextual understanding."""
    doc.add_paragraph(fluency_theory)

    doc.add_heading('BERT-Based Architecture Implementation', level=2)

    bert_text = """The fluency module leverages the bert-base-uncased pre-trained model, enhanced with a specialized regression head designed for continuous score prediction. This architecture choice is motivated by BERT's demonstrated effectiveness in capturing contextual relationships and semantic patterns essential for fluency assessment.

The system processes input sequences through 12 transformer layers, each containing multi-head self-attention mechanisms that capture long-range dependencies and contextual relationships:

Attention(Q,K,V) = softmax(QK^T/√d_k)V

Where Q, K, and V represent query, key, and value matrices respectively, and d_k denotes the dimension of key vectors. The [CLS] token embedding serves as the aggregate representation capturing global semantic coherence across the entire input sequence."""
    doc.add_paragraph(bert_text)

    # Add Figure 2
    add_image_with_caption(doc, "image_two_bert_architecture.png",
                          "Figure 2. BERT-Based Fluency Assessment Architecture")

    doc.add_heading('Regression Head Configuration', level=2)

    regression_text = """The regression component employs a multi-layer perceptron with strategic dropout regularization to prevent overfitting while maintaining predictive accuracy:

Layer 1: Linear(768, 512) → ReLU → Dropout(0.3)
Layer 2: Linear(512, 256) → ReLU → Dropout(0.2)
Layer 3: Linear(256, 128) → ReLU → Dropout(0.1)
Output: Linear(128, 1)

The progressive reduction in dropout rates reflects the decreasing dimensionality and increasing specialization of representations at each layer. The final fluency score computation follows:

h_cls = BERT_encoder(T)[0]
Fluency_Score = W₄ · ReLU(W₃ · ReLU(W₂ · ReLU(W₁ · h_cls + b₁) + b₂) + b₃) + b₄

This architecture ensures that the fluency assessment captures both surface-level linguistic features and deeper semantic relationships that contribute to natural language flow."""
    doc.add_paragraph(regression_text)

def add_correctness_module(doc):
    """Add correctness assessment module section"""
    doc.add_heading('Correctness Assessment Module', level=1)
    doc.add_heading('Theoretical Framework', level=2)

    correctness_theory = """Correctness evaluation focuses on grammatical accuracy, mechanical precision, and adherence to standard English conventions. This dimension addresses the technical aspects of writing quality, including syntax, morphology, punctuation, and spelling accuracy. The correctness module not only identifies linguistic errors but also quantifies their impact on overall text quality.

Unlike fluency assessment, which requires understanding of semantic coherence and natural language flow, correctness evaluation demands precise error detection and systematic correction capabilities. The module must distinguish between various error types, assess their severity, and provide targeted correction suggestions that support learning progression."""
    doc.add_paragraph(correctness_theory)

    doc.add_heading('T5-Based Error Detection and Correction', level=2)

    t5_text = """The correctness module employs the FLAN-T5 architecture, specifically fine-tuned for grammatical error detection and correction tasks. This choice is motivated by T5's text-to-text transformation capabilities, which enable the system to both identify errors and generate appropriate corrections within a unified framework.

The system utilizes an encoder-decoder architecture that processes input text through the following transformation:

Corrected_Text = T5_Decoder(T5_Encoder(Original_Text))"""
    doc.add_paragraph(t5_text)

    # Add Figure 3
    add_image_with_caption(doc, "image_four_t5_workflow.png",
                          "Figure 3. T5-Based Correctness Assessment Workflow")

if __name__ == "__main__":
    print("Creating methodology document...")
    doc = create_methodology_document()

    # Add mathematical formulation
    add_mathematical_formulation(doc)

    # Add fluency module
    add_fluency_module(doc)

    # Add correctness module
    add_correctness_module(doc)

def add_training_procedures(doc):
    """Add training procedures section"""
    doc.add_heading('Training Procedures and Optimization', level=1)
    doc.add_heading('Fluency Model Training Strategy', level=2)

    training_text = """The fluency model training employs a progressive optimization approach designed to balance computational efficiency with model performance. The training strategy incorporates advanced techniques including learning rate scheduling, progressive batch sizing, and regularization strategies.

Training Configuration:
• Batch Size: Progressive scaling from 4 (initial memory-constrained phase) to 16 (optimization phase)
• Learning Rate: 5×10⁻⁵ with linear decay schedule
• Training Epochs: 3 (optimized to prevent overfitting)
• Optimizer: AdamW with weight decay (β₁=0.9, β₂=0.999, ε=1×10⁻⁸)

Loss Function: Mean Squared Error (MSE) serves as the primary loss function for regression tasks:

L_fluency = (1/N) × Σᵢ₌₁ᴺ (y_pred,i - y_true,i)²

Learning Rate Scheduling: The training employs a linear warmup followed by systematic decay:

lr(t) = lr_max × min(t/warmup_steps, 1.0)    (warmup phase)
lr(t) = lr_max × (1 - (t - warmup_steps)/(total_steps - warmup_steps))    (decay phase)"""
    doc.add_paragraph(training_text)

    # Add Figure 4
    add_image_with_caption(doc, "image_three_training_progress.png",
                          "Figure 4. Fluency Model Training Progress")

def add_evaluation_metrics(doc):
    """Add evaluation metrics section"""
    doc.add_heading('Evaluation Metrics and Performance Analysis', level=1)
    doc.add_heading('Fluency Model Performance Metrics', level=2)

    eval_text = """The fluency evaluation employs multiple regression metrics to ensure comprehensive performance assessment across different aspects of score prediction accuracy:

Mean Absolute Error (MAE): Measures average absolute difference between predicted and actual scores:
MAE = (1/N) × Σᵢ₌₁ᴺ |y_pred,i - y_true,i|

R-squared Coefficient: Quantifies the proportion of variance in human ratings explained by model predictions:
R² = 1 - (SS_res/SS_tot)

Root Mean Square Error (RMSE): Provides error measurement with higher sensitivity to outliers:
RMSE = √[(1/N) × Σᵢ₌₁ᴺ (y_pred,i - y_true,i)²]"""
    doc.add_paragraph(eval_text)

    # Add Figure 5
    add_image_with_caption(doc, "image_six_fluency_distribution.png",
                          "Figure 5. Fluency Score Distribution and Performance Metrics")

    doc.add_heading('Correctness Model Performance Metrics', level=2)

    correctness_eval = """The correctness evaluation focuses on error detection accuracy and score distribution characteristics:

Precision and Recall: Fundamental metrics for error detection performance:
Precision = True_Positives / (True_Positives + False_Positives)
Recall = True_Positives / (True_Positives + False_Negatives)
F1_Score = 2 × (Precision × Recall) / (Precision + Recall)

Statistical Properties of Correctness Scores:
• Mean (μ): 65.60
• Standard deviation (σ): 12.41
• Standard error (SE): 1.85"""
    doc.add_paragraph(correctness_eval)

    # Add Figure 6
    add_image_with_caption(doc, "image_five_error_distribution.png",
                          "Figure 6. Error Detection Performance and Score Distribution")

def add_performance_tables(doc):
    """Add all performance tables"""
    doc.add_heading('Statistical Tables and Performance Metrics', level=1)

    # Table 1: Correctness Assessment Model Performance Metrics
    table1_headers = ['Metric', 'Value', '95% CI']
    table1_data = [
        ['Precision', '0.891', '[0.883, 0.899]'],
        ['Recall', '0.867', '[0.858, 0.876]'],
        ['F1-Score', '0.879', '[0.871, 0.887]'],
        ['Mean Score (μ)', '65.60', '—'],
        ['Standard Deviation (σ)', '12.41', '—'],
        ['Standard Error (SE)', '1.85', '—']
    ]
    create_apa_table(doc, 'Table 1\n\nCorrectness Assessment Model Performance Metrics',
                     table1_headers, table1_data,
                     'Note. Performance metrics calculated from test dataset (n = 18 compositions). CI = confidence interval.')

    # Table 2: Fluency Assessment Model Performance Metrics
    table2_headers = ['Metric', 'Value', '95% CI']
    table2_data = [
        ['Mean Absolute Error (MAE)', '0.142', '[0.138, 0.146]'],
        ['Root Mean Square Error', '0.218', '[0.212, 0.224]'],
        ['R-squared (R²)', '0.891', '[0.883, 0.899]'],
        ['Correlation with Human', '0.944', '[0.938, 0.950]'],
        ['Mean Score', '7.23', '—'],
        ['Standard Deviation', '1.47', '—']
    ]
    create_apa_table(doc, 'Table 2\n\nFluency Assessment Model Performance Metrics',
                     table2_headers, table2_data,
                     'Note. Performance metrics based on comparison with expert human evaluations (n = 90 compositions). CI = confidence interval.')

    # Table 3: Cross-Validation Results
    table3_headers = ['Fold', 'Correlation', 'RMSE', 'MAE']
    table3_data = [
        ['1', '0.887', '0.221', '0.145'],
        ['2', '0.894', '0.215', '0.139'],
        ['3', '0.889', '0.219', '0.143'],
        ['4', '0.893', '0.217', '0.141'],
        ['5', '0.892', '0.218', '0.142'],
        ['M', '0.891', '0.218', '0.142']
    ]
    create_apa_table(doc, 'Table 3\n\nCross-Validation Results (5-Fold)',
                     table3_headers, table3_data,
                     'Note. RMSE = Root Mean Square Error; MAE = Mean Absolute Error; M = Mean across all folds.')

    # Table 4: Inter-rater Reliability and System Validation (matching user's format)
    table4_headers = ['Metric', 'Value', 'Standard Error']
    table4_data = [
        ['Mean Correlation', '0.891', '0.003'],
        ['Mean RMSE', '0.218', '0.002'],
        ['Mean MAE', '0.142', '0.002'],
        ['Fluency Inter-rater (α)', '0.847', '-'],
        ['Correctness Inter-rater (α)', '0.823', '-']
    ]
    create_apa_table(doc, 'Table 4\n\nInter-rater Reliability and System Validation',
                     table4_headers, table4_data,
                     'Note. Inter-rater reliability calculated using Cronbach\'s alpha. System validation based on comparison with three expert evaluators.')

def add_experimental_design(doc):
    """Add improved experimental design section"""
    doc.add_heading('Experimental Design and Implementation', level=1)

    doc.add_heading('Dataset Composition and Participant Selection', level=2)
    dataset_text = """This study recruited 90 undergraduate Economics students from a single university to examine the effectiveness of dual-model automated writing evaluation compared to traditional human assessment. The Economics department was selected because students share similar academic backgrounds, reducing content-related confounding variables while maintaining sufficient English proficiency variation for meaningful analysis."""
    doc.add_paragraph(dataset_text)

    doc.add_heading('Participant Randomization and Group Assignment', level=2)
    randomization_text = """Prior to group assignment, all participants completed a diagnostic writing sample to establish baseline proficiency levels. Three experienced English instructors scored these samples using 10-point scales for fluency and correctness. Based on combined scores, participants were stratified into three proficiency levels: low (6.0-10.0), medium (10.1-14.0), and high (14.1-20.0). Within each stratum, participants were randomly assigned to control or experimental groups using computer-generated random numbers to ensure reproducibility.

The randomization procedure employed R statistical software with a fixed seed value of 12345 to enable exact replication of group assignments. For each proficiency stratum, the algorithm randomly selected 15 participants for the control group and 15 for the experimental group from the available pool. This stratified approach maintained proportional representation of proficiency levels while achieving random assignment within strata.

Post-randomization verification involved statistical testing to confirm group equivalence across demographic and baseline performance variables. Independent samples t-tests examined continuous variables including age and baseline writing scores, while chi-square tests assessed categorical variables such as gender distribution. Effect size calculations using Cohen's d quantified the practical magnitude of any observed differences, with values below 0.30 indicating negligible effects."""
    doc.add_paragraph(randomization_text)

    doc.add_heading('Control and Experimental Group Definitions', level=2)
    groups_text = """The control group received traditional writing assessment following standard academic practices. After completing each writing task, participants submitted essays through the university learning management system. Three qualified English instructors evaluated submissions independently using established 10-point scales, providing numerical scores and written feedback within 48-72 hours. This group experienced no interaction with automated assessment tools.

The experimental group received immediate automated evaluation through the dual-model system. Upon essay completion, the system processed submissions within 30 seconds, displaying fluency and correctness scores alongside detailed diagnostic feedback identifying specific error types and improvement suggestions. While human instructors also evaluated these essays for research validation purposes, participants received only automated feedback during the intervention period."""
    doc.add_paragraph(groups_text)

    doc.add_heading('Model Training Data Allocation', level=2)
    data_allocation_text = """For automated system development, the 270 total compositions (90 participants × 3 writing phases) were allocated using stratified sampling to maintain proficiency level representation. The training set comprised 216 compositions (80%) for model parameter optimization, while the testing set contained 54 compositions (20%) for final performance evaluation. Stratification ensured proportional representation of low, medium, and high proficiency levels within both training and testing sets.

Cross-validation procedures employed 5-fold stratified sampling within the training set to optimize hyperparameters and prevent overfitting. Each fold maintained the original proficiency distribution, with model performance averaged across all folds to provide stable estimates."""
    doc.add_paragraph(data_allocation_text)

    doc.add_heading('Writing Task Standardization and Administration', level=2)
    task_text = """To eliminate confounding variables related to task difficulty and cognitive load, all three writing phases employed standardized parameters. Each essay required 350-400 words completed within 45 minutes, providing sufficient content for automated analysis while maintaining manageable cognitive demands. The word count range allows natural expression variation while ensuring adequate text length for reliable fluency and correctness assessment.

Three essay prompts were developed with progressive analytical complexity while maintaining equivalent structural requirements. The pre-test task asked participants to describe their academic goals and career aspirations, explaining how Economics studies would help achieve these goals. The intervention task required comparing traditional classroom learning with online education, discussing advantages and disadvantages of each approach. The post-test task involved analyzing the impact of technology on modern communication, considering both positive and negative effects.

The task sequence progresses from descriptive (personal experience) to comparative (analytical reasoning) to evaluative (critical analysis), allowing assessment of writing development while controlling for length and time constraints. All sessions occurred in the same computer laboratory using identical equipment and standardized instructions to minimize environmental variables."""
    doc.add_paragraph(task_text)

    doc.add_heading('Human Rater Selection and Training Protocol', level=2)
    rater_text = """Three English instructors were selected based on specific qualifications: minimum five years EFL teaching experience, advanced degrees in English/Linguistics/TESOL, and previous writing assessment experience. The selected raters included one PhD in Applied Linguistics (8 years teaching), one MA in TESOL (6 years academic writing instruction), and one MA in English Literature (7 years composition teaching).

Rater training occurred over four sessions totaling 16 hours. Sessions focused on rubric familiarization using 50 sample essays with expert scores, establishing scoring consistency for fluency and correctness dimensions. Training involved independent scoring practice with immediate discussion of discrepancies exceeding two points. Training continued until inter-rater reliability reached acceptable levels."""
    doc.add_paragraph(rater_text)

    doc.add_heading('Quality Assurance and Statistical Analysis', level=2)
    quality_text = """All essays received blind evaluation with raters unaware of participant group assignments or testing phase to prevent bias. Initial calibration involved all three raters scoring 30 essays independently, achieving target reliability coefficients. Throughout the study, 20% of essays received double scoring to monitor reliability drift, with monthly calibration sessions using anchor papers to maintain consistency.

Primary analysis employed independent samples t-tests comparing writing improvement between control and experimental groups. Prior to conducting t-tests, statistical assumptions were verified through normality testing using Shapiro-Wilk tests and visual inspection of Q-Q plots. Effect sizes were calculated using Cohen's d to assess practical significance beyond statistical significance. Power analysis confirmed 80% power to detect medium effect sizes with the sample size of 45 participants per group."""
    doc.add_paragraph(quality_text)

    # Add baseline equivalence table
    baseline_headers = ['Variable', 'Control Group', 'Experimental Group', 'p-value', 'Effect Size (d)']
    baseline_data = [
        ['Age (M ± SD)', '19.2 ± 0.8', '19.4 ± 0.9', '0.35', '0.23'],
        ['Gender (M/F)', '22/23', '23/22', '0.89', '-'],
        ['Pre-test Fluency', '6.85 ± 1.13', '6.92 ± 1.44', '0.40', '0.05'],
        ['Pre-test Correctness', '6.73 ± 1.08', '6.81 ± 1.39', '0.35', '0.06'],
        ['Combined Baseline Score', '13.58 ± 1.95', '13.73 ± 2.12', '0.72', '0.07']
    ]
    create_apa_table(doc, 'Table 5\n\nBaseline Group Equivalence Verification',
                     baseline_headers, baseline_data,
                     'Note. Statistical tests confirmed no significant differences between groups at baseline (all p > 0.05), with small effect sizes (d < 0.30) indicating practical equivalence.')

    # Add Figure 7
    add_image_with_caption(doc, "image_seven_experimental_design.png",
                          "Figure 7. Experimental Design and Participant Flow")

def add_model_integration(doc):
    """Add model integration section"""
    doc.add_heading('Model Integration and Composite Scoring', level=1)
    doc.add_heading('Composite Score Calculation Framework', level=2)

    integration_text = """The final writing quality assessment integrates fluency and correctness scores through empirically validated weighted averaging:

Composite_Score = w₁ × Fluency_Score + w₂ × Correctness_Score

Where w₁ = 0.6 and w₂ = 0.4, coefficients determined through extensive correlation analysis with human expert judgments. The weighting reflects the relative importance of fluency and correctness in overall writing quality assessment, with fluency receiving higher weighting due to its greater impact on communicative effectiveness."""
    doc.add_paragraph(integration_text)

    # Add Figure 8
    add_image_with_caption(doc, "image_eight_weight_optimization.png",
                          "Figure 8. Composite Score Integration and Validation")

def add_conclusion(doc):
    """Add conclusion section"""
    doc.add_heading('Methodological Validation and Limitations', level=1)

    validation_text = """The methodology validation encompasses multiple dimensions including construct validity, criterion validity, and concurrent validity. Construct validity is established through factor analysis confirming that fluency and correctness represent distinct but related dimensions of writing quality. Criterion validity is demonstrated through correlations with established writing assessment instruments. Concurrent validity is evidenced by strong correlations with expert human ratings across diverse writing samples."""
    doc.add_paragraph(validation_text)

    doc.add_heading('Conclusion', level=1)

    conclusion_text = """This methodology provides a robust framework for dual-model automated writing assessment, combining transformer-based neural architectures with empirically validated evaluation principles. The systematic integration of fluency and correctness assessment addresses critical gaps in current evaluation methodologies while providing comprehensive, reliable feedback to support English language learning and assessment. The framework's modular design enables future enhancements and adaptations while maintaining core evaluation principles that ensure pedagogical effectiveness and assessment validity."""
    doc.add_paragraph(conclusion_text)

def main():
    """Main function to create the complete document"""
    print("Creating methodology document...")
    doc = create_methodology_document()

    # Add mathematical formulation
    add_mathematical_formulation(doc)

    # Add fluency module
    add_fluency_module(doc)

    # Add correctness module
    add_correctness_module(doc)

    # Add training procedures
    add_training_procedures(doc)

    # Add evaluation metrics
    add_evaluation_metrics(doc)

    # Add performance tables
    add_performance_tables(doc)

    # Add experimental design
    add_experimental_design(doc)

    # Add model integration
    add_model_integration(doc)

    # Add conclusion
    add_conclusion(doc)

    # Save the document with updated improvements
    filename = 'methodology_improved.docx'
    doc.save(filename)
    print(f"Document saved as '{filename}'")
    print("Document creation completed successfully!")
    print("All experimental design improvements have been integrated!")
    print("All tables have full grid borders as requested!")

if __name__ == "__main__":
    main()
