# Dual-Model Automated Writing Assessment - Figure Generation

This repository contains Python code to generate all figures for the dual-model automated writing assessment research paper, exactly matching the HTML visualizations.

## Figures Generated

1. **Figure 1**: Dual-Model System Architecture Overview
2. **Figure 2**: BERT-Based Fluency Assessment Architecture  
3. **Figure 3**: T5-Based Correctness Assessment Workflow
4. **Figure 4**: Fluency Model Training Progress
5. **Figure 5**: Experimental Design and Results Visualization
6. **Figure 6**: Score Distribution and Correlation Analysis
7. **Figure 7**: System Architecture and Integration Framework

## Installation

1. Install required packages:
```bash
pip install -r requirements.txt
```

## Usage

### Generate All Figures
```bash
python dual_model_figures.py
```
or
```bash
python run_figures.py
```

### Generate Individual Figure
```bash
python run_figures.py 1  # Generate Figure 1
python run_figures.py 2  # Generate Figure 2
# ... etc
```

### Programmatic Usage
```python
from dual_model_figures import DualModelFigures

# Initialize generator
generator = DualModelFigures()

# Generate specific figure
fig1 = generator.figure1_system_architecture()
fig1.show()

# Generate all figures
generator.generate_all_figures()
```

## Output

- All figures are saved as high-resolution PNG files (300 DPI) in the `figures/` directory
- Files are named: `figure1_system_architecture.png`, `figure2_bert_architecture.png`, etc.
- **NO TABLES IN IMAGES** - All statistical tables are integrated in the methodology text
- Modern, visually appealing design with icons, gradients, and professional styling

## Figure Details

### Figure 1: System Architecture
- Complete dual-model workflow
- Mathematical formulations
- Component interactions
- Technical specifications

### Figure 2: BERT Architecture
- Input tokenization process
- Transformer layer visualization
- Regression head details
- Attention mechanism equations

### Figure 3: T5 Workflow
- Error detection and correction process
- Encoder-decoder architecture
- Differential analysis
- Scoring calculations

### Figure 4: Training Progress
- Loss curves (training/validation)
- Learning rate schedule
- Performance metrics evolution
- Batch size progression

### Figure 5: Experimental Design
- Participant flow diagram
- Randomization process
- Results visualization
- Statistical significance

### Figure 6: Performance Analysis
- Score distributions
- Correlation analysis
- Human vs automated comparison
- Performance metrics

### Figure 7: System Integration
- Complete architecture framework
- Performance comparison table
- Validation results
- Technical specifications

## Customization

The `DualModelFigures` class can be easily customized:

```python
# Modify colors
generator = DualModelFigures()
generator.colors['primary'] = '#your_color'

# Adjust figure sizes
fig = generator.figure1_system_architecture()
fig.set_size_inches(16, 12)
```

## Academic Publication Ready

- High-resolution output (300 DPI)
- Modern, visually appealing design with icons and gradients
- Professional academic styling with enhanced visual elements
- Consistent color scheme optimized for both print and digital
- Publication-quality typography
- **NO TABLES IN IMAGES** - Clean, focused visualizations
- All statistical data integrated in methodology text
- Enhanced visual hierarchy and modern design elements

## Requirements

- Python 3.7+
- matplotlib >= 3.5.0
- numpy >= 1.21.0
- seaborn >= 0.11.0
- pandas >= 1.3.0
- scipy >= 1.7.0

## Notes

- Figures are generated with synthetic data that matches the statistical properties described in the methodology
- All mathematical formulations are included as specified in the research paper
- Color scheme is optimized for both print and digital publication
- Figures can be easily modified for different output formats (PDF, SVG, etc.)
