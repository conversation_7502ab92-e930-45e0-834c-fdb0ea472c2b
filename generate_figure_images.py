import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
import time

def setup_driver():
    """Setup Chrome driver for screenshot capture"""
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1200,800")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-extensions")

    try:
        # Try to use webdriver-manager if available
        try:
            from webdriver_manager.chrome import ChromeDriverManager
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
        except ImportError:
            # Fallback to system ChromeDriver
            driver = webdriver.Chrome(options=chrome_options)
        return driver
    except Exception as e:
        print(f"Error setting up Chrome driver: {e}")
        print("Please make sure Chrome is installed")
        return None

def capture_figures():
    """Capture individual figures as PNG images"""
    driver = setup_driver()
    if not driver:
        return False
    
    try:
        # Get absolute path to HTML file
        html_file = os.path.abspath("create_figures.html")
        driver.get(f"file://{html_file}")
        
        # Wait for page to load
        time.sleep(2)
        
        # Find all figure containers
        figure_containers = driver.find_elements(By.CLASS_NAME, "figure-container")
        
        if not os.path.exists("figures"):
            os.makedirs("figures")
        
        for i, container in enumerate(figure_containers, 1):
            # Scroll to the figure
            driver.execute_script("arguments[0].scrollIntoView();", container)
            time.sleep(1)
            
            # Take screenshot of the specific figure
            filename = f"figures/figure_{i}.png"
            container.screenshot(filename)
            print(f"Saved {filename}")
        
        print("All figures captured successfully!")
        return True
        
    except Exception as e:
        print(f"Error capturing figures: {e}")
        return False
    finally:
        driver.quit()

def create_individual_html_files():
    """Create individual HTML files for each figure for manual screenshot if needed"""
    
    figures_html = [
        # Figure 1 HTML
        '''<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Figure 1</title>
<style>
body { font-family: 'Times New Roman', serif; margin: 20px; background: white; }
.figure-title { text-align: center; font-weight: bold; font-size: 14px; margin-bottom: 20px; }
.diagram { width: 100%; height: 600px; border: 1px solid #ddd; }
.box { fill: #e8f4fd; stroke: #2c5aa0; stroke-width: 2; rx: 8; }
.process-box { fill: #fff2cc; stroke: #d6b656; stroke-width: 2; rx: 8; }
.output-box { fill: #d5e8d4; stroke: #82b366; stroke-width: 2; rx: 8; }
.neural-box { fill: #f8cecc; stroke: #b85450; stroke-width: 2; rx: 8; }
.text { font-family: 'Times New Roman', serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; fill: #333; }
.small-text { font-size: 10px; }
.arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
</style></head><body>
<div class="figure-title">Figure 1. Dual-Model System Architecture Schema</div>
<svg class="diagram" viewBox="0 0 800 600">
<defs><marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
<polygon points="0 0, 10 3.5, 0 7" fill="#333" /></marker></defs>
<rect class="box" x="350" y="50" width="100" height="40"/>
<text class="text" x="400" y="70">Input Text</text>
<rect class="process-box" x="325" y="120" width="150" height="40"/>
<text class="text" x="400" y="140">Text Preprocessing</text>
<rect class="neural-box" x="150" y="200" width="120" height="60"/>
<text class="text" x="210" y="220">BERT-based</text>
<text class="text" x="210" y="240">Fluency Module</text>
<rect class="neural-box" x="530" y="200" width="120" height="60"/>
<text class="text" x="590" y="220">T5-based</text>
<text class="text" x="590" y="240">Correctness Module</text>
<rect class="output-box" x="150" y="300" width="120" height="40"/>
<text class="text" x="210" y="320">Fluency Score (Sf)</text>
<rect class="output-box" x="530" y="300" width="120" height="40"/>
<text class="text" x="590" y="320">Correctness Score (Sc)</text>
<rect class="process-box" x="325" y="380" width="150" height="60"/>
<text class="text" x="400" y="400">Score Integration</text>
<text class="text small-text" x="400" y="420">w₁ × Sf + w₂ × Sc</text>
<rect class="output-box" x="325" y="480" width="150" height="40"/>
<text class="text" x="400" y="500">Final Assessment Score</text>
<line class="arrow" x1="400" y1="90" x2="400" y2="120"/>
<line class="arrow" x1="350" y1="160" x2="210" y2="200"/>
<line class="arrow" x1="450" y1="160" x2="590" y2="200"/>
<line class="arrow" x1="210" y1="260" x2="210" y2="300"/>
<line class="arrow" x1="590" y1="260" x2="590" y2="300"/>
<line class="arrow" x1="270" y1="320" x2="350" y2="380"/>
<line class="arrow" x1="530" y1="320" x2="450" y2="380"/>
<line class="arrow" x1="400" y1="440" x2="400" y2="480"/>
<text class="text small-text" x="280" y="180">Parallel Processing</text>
</svg></body></html>''',

        # Figure 2 HTML
        '''<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Figure 2</title>
<style>
body { font-family: 'Times New Roman', serif; margin: 20px; background: white; }
.figure-title { text-align: center; font-weight: bold; font-size: 14px; margin-bottom: 20px; }
.diagram { width: 100%; height: 600px; border: 1px solid #ddd; }
.box { fill: #e8f4fd; stroke: #2c5aa0; stroke-width: 2; rx: 8; }
.process-box { fill: #fff2cc; stroke: #d6b656; stroke-width: 2; rx: 8; }
.output-box { fill: #d5e8d4; stroke: #82b366; stroke-width: 2; rx: 8; }
.neural-box { fill: #f8cecc; stroke: #b85450; stroke-width: 2; rx: 8; }
.text { font-family: 'Times New Roman', serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; fill: #333; }
.arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
</style></head><body>
<div class="figure-title">Figure 2. BERT-based Fluency Assessment Model Architecture</div>
<svg class="diagram" viewBox="0 0 800 600">
<defs><marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
<polygon points="0 0, 10 3.5, 0 7" fill="#333" /></marker></defs>
<rect class="box" x="350" y="50" width="100" height="30"/>
<text class="text" x="400" y="65">Input Text</text>
<rect class="process-box" x="300" y="100" width="200" height="30"/>
<text class="text" x="400" y="115">WordPiece Tokenization</text>
<rect class="neural-box" x="50" y="160" width="120" height="40"/>
<text class="text" x="110" y="175">Token</text>
<text class="text" x="110" y="190">Embeddings</text>
<rect class="neural-box" x="200" y="160" width="120" height="40"/>
<text class="text" x="260" y="175">Position</text>
<text class="text" x="260" y="190">Embeddings</text>
<rect class="neural-box" x="350" y="160" width="120" height="40"/>
<text class="text" x="410" y="175">Segment</text>
<text class="text" x="410" y="190">Embeddings</text>
<rect class="neural-box" x="250" y="240" width="300" height="40"/>
<text class="text" x="400" y="260">12 Transformer Layers (Multi-Head Attention)</text>
<rect class="output-box" x="325" y="320" width="150" height="40"/>
<text class="text" x="400" y="340">[CLS] Token Representation</text>
<rect class="process-box" x="300" y="400" width="200" height="30"/>
<text class="text" x="400" y="415">Regression Head (FC + Dropout)</text>
<rect class="process-box" x="325" y="460" width="150" height="30"/>
<text class="text" x="400" y="475">Sigmoid Activation</text>
<rect class="output-box" x="325" y="520" width="150" height="30"/>
<text class="text" x="400" y="535">Fluency Score [0,1]</text>
<line class="arrow" x1="400" y1="80" x2="400" y2="100"/>
<line class="arrow" x1="350" y1="130" x2="110" y2="160"/>
<line class="arrow" x1="400" y1="130" x2="260" y2="160"/>
<line class="arrow" x1="450" y1="130" x2="410" y2="160"/>
<line class="arrow" x1="200" y1="200" x2="300" y2="240"/>
<line class="arrow" x1="400" y1="200" x2="400" y2="240"/>
<line class="arrow" x1="500" y1="200" x2="500" y2="240"/>
<line class="arrow" x1="400" y1="280" x2="400" y2="320"/>
<line class="arrow" x1="400" y1="360" x2="400" y2="400"/>
<line class="arrow" x1="400" y1="430" x2="400" y2="460"/>
<line class="arrow" x1="400" y1="490" x2="400" y2="520"/>
</svg></body></html>''',

        # Figure 3 HTML
        '''<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Figure 3</title>
<style>
body { font-family: 'Times New Roman', serif; margin: 20px; background: white; }
.figure-title { text-align: center; font-weight: bold; font-size: 14px; margin-bottom: 20px; }
.diagram { width: 100%; height: 600px; border: 1px solid #ddd; }
.box { fill: #e8f4fd; stroke: #2c5aa0; stroke-width: 2; rx: 8; }
.process-box { fill: #fff2cc; stroke: #d6b656; stroke-width: 2; rx: 8; }
.output-box { fill: #d5e8d4; stroke: #82b366; stroke-width: 2; rx: 8; }
.neural-box { fill: #f8cecc; stroke: #b85450; stroke-width: 2; rx: 8; }
.text { font-family: 'Times New Roman', serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; fill: #333; }
.small-text { font-size: 10px; }
.arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
</style></head><body>
<div class="figure-title">Figure 3. T5-based Correctness Assessment Workflow</div>
<svg class="diagram" viewBox="0 0 800 600">
<defs><marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
<polygon points="0 0, 10 3.5, 0 7" fill="#333" /></marker></defs>
<rect class="box" x="350" y="50" width="100" height="30"/>
<text class="text" x="400" y="65">Original Text</text>
<rect class="process-box" x="300" y="100" width="200" height="30"/>
<text class="text" x="400" y="115">Text Normalization & Tokenization</text>
<rect class="neural-box" x="150" y="170" width="150" height="60"/>
<text class="text" x="225" y="190">T5 Encoder</text>
<text class="text small-text" x="225" y="210">Contextualized</text>
<text class="text small-text" x="225" y="225">Representations</text>
<rect class="neural-box" x="500" y="170" width="150" height="60"/>
<text class="text" x="575" y="190">T5 Decoder</text>
<text class="text small-text" x="575" y="210">Error Correction</text>
<text class="text small-text" x="575" y="225">Generation</text>
<rect class="output-box" x="500" y="270" width="150" height="40"/>
<text class="text" x="575" y="290">Corrected Text</text>
<rect class="process-box" x="300" y="350" width="200" height="60"/>
<text class="text" x="400" y="370">Error Quantification</text>
<text class="text small-text" x="400" y="385">Token-level Comparison</text>
<text class="text small-text" x="400" y="400">Error Categorization</text>
<rect class="process-box" x="150" y="450" width="150" height="40"/>
<text class="text" x="225" y="470">Error Ratio</text>
<text class="text small-text" x="225" y="485">Corrections/Tokens</text>
<rect class="process-box" x="500" y="450" width="150" height="40"/>
<text class="text" x="575" y="470">Exponential Decay</text>
<text class="text small-text" x="575" y="485">100 × e^(-λ × ratio)</text>
<rect class="output-box" x="325" y="530" width="150" height="30"/>
<text class="text" x="400" y="545">Correctness Score</text>
<line class="arrow" x1="400" y1="80" x2="400" y2="100"/>
<line class="arrow" x1="350" y1="130" x2="225" y2="170"/>
<line class="arrow" x1="300" y1="200" x2="500" y2="200"/>
<line class="arrow" x1="575" y1="230" x2="575" y2="270"/>
<line class="arrow" x1="450" y1="130" x2="575" y2="170"/>
<line class="arrow" x1="500" y1="290" x2="450" y2="350"/>
<line class="arrow" x1="350" y1="130" x2="350" y2="350"/>
<line class="arrow" x1="350" y1="410" x2="225" y2="450"/>
<line class="arrow" x1="450" y1="410" x2="575" y2="450"/>
<line class="arrow" x1="300" y1="470" x2="350" y2="530"/>
<line class="arrow" x1="500" y1="470" x2="450" y2="530"/>
</svg></body></html>''',

        # Figure 4 HTML
        '''<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Figure 4</title>
<style>
body { font-family: 'Times New Roman', serif; margin: 20px; background: white; }
.figure-title { text-align: center; font-weight: bold; font-size: 14px; margin-bottom: 20px; }
.diagram { width: 100%; height: 600px; border: 1px solid #ddd; }
.chart-line { fill: none; stroke-width: 2; }
.training-line { stroke: #2c5aa0; }
.validation-line { stroke: #d6b656; }
.grid-line { stroke: #ddd; stroke-width: 0.5; }
.axis { stroke: #333; stroke-width: 1; }
.axis-text { font-size: 10px; text-anchor: middle; fill: #333; }
.text { font-family: 'Times New Roman', serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; fill: #333; }
.small-text { font-size: 10px; }
</style></head><body>
<div class="figure-title">Figure 4. Training Progress Analysis for Fluency Assessment Model</div>
<svg class="diagram" viewBox="0 0 800 600">
<rect x="100" y="80" width="600" height="400" fill="white" stroke="#ddd"/>
<line class="grid-line" x1="100" y1="130" x2="700" y2="130"/>
<line class="grid-line" x1="100" y1="180" x2="700" y2="180"/>
<line class="grid-line" x1="100" y1="230" x2="700" y2="230"/>
<line class="grid-line" x1="100" y1="280" x2="700" y2="280"/>
<line class="grid-line" x1="100" y1="330" x2="700" y2="330"/>
<line class="grid-line" x1="100" y1="380" x2="700" y2="380"/>
<line class="grid-line" x1="100" y1="430" x2="700" y2="430"/>
<line class="grid-line" x1="200" y1="80" x2="200" y2="480"/>
<line class="grid-line" x1="300" y1="80" x2="300" y2="480"/>
<line class="grid-line" x1="400" y1="80" x2="400" y2="480"/>
<line class="grid-line" x1="500" y1="80" x2="500" y2="480"/>
<line class="grid-line" x1="600" y1="80" x2="600" y2="480"/>
<line class="axis" x1="100" y1="480" x2="700" y2="480"/>
<line class="axis" x1="100" y1="80" x2="100" y2="480"/>
<polyline class="chart-line training-line" points="100,450 200,380 300,320 400,280 500,250 600,230 700,220"/>
<polyline class="chart-line validation-line" points="100,460 200,400 300,350 400,320 500,300 600,290 700,285"/>
<text class="axis-text" x="400" y="510">Training Epochs</text>
<text class="axis-text" x="50" y="280" transform="rotate(-90 50 280)">Loss (MSE)</text>
<text class="axis-text" x="100" y="500">0</text>
<text class="axis-text" x="200" y="500">0.5</text>
<text class="axis-text" x="300" y="500">1.0</text>
<text class="axis-text" x="400" y="500">1.5</text>
<text class="axis-text" x="500" y="500">2.0</text>
<text class="axis-text" x="600" y="500">2.5</text>
<text class="axis-text" x="700" y="500">3.0</text>
<text class="axis-text" x="85" y="485">0.0</text>
<text class="axis-text" x="85" y="435">0.1</text>
<text class="axis-text" x="85" y="385">0.2</text>
<text class="axis-text" x="85" y="335">0.3</text>
<text class="axis-text" x="85" y="285">0.4</text>
<text class="axis-text" x="85" y="235">0.5</text>
<text class="axis-text" x="85" y="185">0.6</text>
<text class="axis-text" x="85" y="135">0.7</text>
<text class="axis-text" x="85" y="85">0.8</text>
<rect x="520" y="120" width="150" height="60" fill="white" stroke="#333"/>
<line class="chart-line training-line" x1="530" y1="135" x2="560" y2="135"/>
<text class="text small-text" x="570" y="140">Training Loss</text>
<line class="chart-line validation-line" x1="530" y1="155" x2="560" y2="155"/>
<text class="text small-text" x="570" y="160">Validation Loss</text>
<rect x="520" y="200" width="150" height="80" fill="#f9f9f9" stroke="#333"/>
<text class="text small-text" x="595" y="220">Final Metrics:</text>
<text class="text small-text" x="595" y="240">Correlation: 0.944</text>
<text class="text small-text" x="595" y="255">MAE: 0.142</text>
<text class="text small-text" x="595" y="270">RMSE: 0.218</text>
</svg></body></html>'''
    ]
    
    # Create individual HTML files
    for i, html_content in enumerate(figures_html, 1):
        filename = f"figure_{i}.html"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"Created {filename}")

if __name__ == "__main__":
    print("Creating individual HTML files for figures...")
    create_individual_html_files()
    
    print("\nAttempting to capture figures using Selenium...")
    success = capture_figures()
    
    if not success:
        print("\nSelenium capture failed. You can:")
        print("1. Open the individual HTML files in a browser and take screenshots manually")
        print("2. Install Chrome and ChromeDriver to use automated capture")
        print("3. Use the main create_figures.html file to view all figures")
    
    print("\nFigure generation complete!")
