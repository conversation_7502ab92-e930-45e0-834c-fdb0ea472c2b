# Research Figures Summary

## Successfully Created Figures for Methodology Paper

### 📊 **Figure Files Generated:**

#### **Main Figures (PNG format):**
- `figures/figure_1.png` - **Figure 1: Dual-Model System Architecture Schema**
- `figures/figure_2.png` - **Figure 2: BERT-based Fluency Assessment Model Architecture**
- `figures/figure_3.png` - **Figure 3: T5-based Correctness Assessment Workflow**
- `figures/figure_4.png` - **Figure 4: Training Progress Analysis for Fluency Assessment Model**

#### **Individual HTML Files (for editing/viewing):**
- `figure_1.html` - Standalone HTML for Figure 1
- `figure_2.html` - Standalone HTML for Figure 2
- `figure_3.html` - Standalone HTML for Figure 3
- `figure_4.html` - Standalone HTML for Figure 4

#### **Master HTML File:**
- `create_figures.html` - Contains all figures in one document

---

## 📋 **Figure Descriptions:**

### **Figure 1: Dual-Model System Architecture Schema**
- **Purpose**: Shows the overall system architecture with parallel processing
- **Components**: 
  - Input text preprocessing
  - BERT-based fluency module
  - T5-based correctness module
  - Score integration (w₁ × Sf + w₂ × Sc)
  - Final assessment output
- **Visual Style**: Clean flowchart with color-coded components

### **Figure 2: BERT-based Fluency Assessment Model Architecture**
- **Purpose**: Detailed view of the BERT fluency assessment pipeline
- **Components**:
  - WordPiece tokenization
  - Token, position, and segment embeddings
  - 12 transformer layers with multi-head attention
  - [CLS] token representation
  - Regression head with dropout
  - Sigmoid activation for final score
- **Visual Style**: Layered architecture diagram

### **Figure 3: T5-based Correctness Assessment Workflow**
- **Purpose**: Shows the T5 error detection and correction process
- **Components**:
  - Text normalization and tokenization
  - T5 encoder for contextualized representations
  - T5 decoder for error correction generation
  - Error quantification and categorization
  - Exponential decay scoring function
- **Visual Style**: Process flow with encoder-decoder architecture

### **Figure 4: Training Progress Analysis**
- **Purpose**: Displays model training convergence and performance metrics
- **Components**:
  - Training and validation loss curves
  - Epoch progression (0-3 epochs)
  - Final performance metrics (Correlation: 0.944, MAE: 0.142, RMSE: 0.218)
  - Legend and grid for clarity
- **Visual Style**: Professional line chart with metrics box

---

## 🎨 **Design Features:**

### **Color Scheme:**
- **Input/Data**: Light blue (#e8f4fd)
- **Processing**: Light yellow (#fff2cc)
- **Neural Networks**: Light red (#f8cecc)
- **Output**: Light green (#d5e8d4)

### **Typography:**
- **Font**: Times New Roman (academic standard)
- **Sizes**: 14px for titles, 12px for main text, 10px for details

### **Professional Elements:**
- Clean, minimal design
- No excessive textual data in images
- Clear visual hierarchy
- Consistent styling across all figures
- Academic publication quality

---

## 📁 **File Organization:**

```
├── figures/
│   ├── figure_1.png          # Main Figure 1
│   ├── figure_2.png          # Main Figure 2
│   ├── figure_3.png          # Main Figure 3
│   └── figure_4.png          # Main Figure 4
├── figure_1.html             # Editable HTML for Figure 1
├── figure_2.html             # Editable HTML for Figure 2
├── figure_3.html             # Editable HTML for Figure 3
├── figure_4.html             # Editable HTML for Figure 4
└── create_figures.html       # All figures combined
```

---

## ✅ **Quality Assurance:**

- **Resolution**: High-quality PNG images suitable for publication
- **Clarity**: Clean, readable text and diagrams
- **Consistency**: Uniform styling across all figures
- **Academic Standards**: Follows research publication guidelines
- **Accessibility**: Clear contrast and readable fonts

---

## 🔧 **Technical Details:**

- **Generated using**: Selenium WebDriver with Chrome
- **Format**: PNG images (publication-ready)
- **Source**: SVG-based HTML for scalability
- **Automation**: Fully automated generation process
- **Customizable**: HTML source files allow easy modifications

The figures are now ready for inclusion in your research paper and perfectly align with the descriptions provided in the methodology.txt file.
