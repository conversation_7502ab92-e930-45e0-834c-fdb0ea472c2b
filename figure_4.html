<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Figure 4</title>
<style>
body { font-family: 'Times New Roman', serif; margin: 20px; background: white; }
.figure-title { text-align: center; font-weight: bold; font-size: 14px; margin-bottom: 20px; }
.diagram { width: 100%; height: 600px; border: 1px solid #ddd; }
.chart-line { fill: none; stroke-width: 2; }
.training-line { stroke: #2c5aa0; }
.validation-line { stroke: #d6b656; }
.grid-line { stroke: #ddd; stroke-width: 0.5; }
.axis { stroke: #333; stroke-width: 1; }
.axis-text { font-size: 10px; text-anchor: middle; fill: #333; }
.text { font-family: 'Times New Roman', serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; fill: #333; }
.small-text { font-size: 10px; }
</style></head><body>
<div class="figure-title">Figure 4. Training Progress Analysis for Fluency Assessment Model</div>
<svg class="diagram" viewBox="0 0 800 600">
<rect x="100" y="80" width="600" height="400" fill="white" stroke="#ddd"/>
<line class="grid-line" x1="100" y1="130" x2="700" y2="130"/>
<line class="grid-line" x1="100" y1="180" x2="700" y2="180"/>
<line class="grid-line" x1="100" y1="230" x2="700" y2="230"/>
<line class="grid-line" x1="100" y1="280" x2="700" y2="280"/>
<line class="grid-line" x1="100" y1="330" x2="700" y2="330"/>
<line class="grid-line" x1="100" y1="380" x2="700" y2="380"/>
<line class="grid-line" x1="100" y1="430" x2="700" y2="430"/>
<line class="grid-line" x1="200" y1="80" x2="200" y2="480"/>
<line class="grid-line" x1="300" y1="80" x2="300" y2="480"/>
<line class="grid-line" x1="400" y1="80" x2="400" y2="480"/>
<line class="grid-line" x1="500" y1="80" x2="500" y2="480"/>
<line class="grid-line" x1="600" y1="80" x2="600" y2="480"/>
<line class="axis" x1="100" y1="480" x2="700" y2="480"/>
<line class="axis" x1="100" y1="80" x2="100" y2="480"/>
<polyline class="chart-line training-line" points="100,450 200,380 300,320 400,280 500,250 600,230 700,220"/>
<polyline class="chart-line validation-line" points="100,460 200,400 300,350 400,320 500,300 600,290 700,285"/>
<text class="axis-text" x="400" y="510">Training Epochs</text>
<text class="axis-text" x="50" y="280" transform="rotate(-90 50 280)">Loss (MSE)</text>
<text class="axis-text" x="100" y="500">0</text>
<text class="axis-text" x="200" y="500">0.5</text>
<text class="axis-text" x="300" y="500">1.0</text>
<text class="axis-text" x="400" y="500">1.5</text>
<text class="axis-text" x="500" y="500">2.0</text>
<text class="axis-text" x="600" y="500">2.5</text>
<text class="axis-text" x="700" y="500">3.0</text>
<text class="axis-text" x="85" y="485">0.0</text>
<text class="axis-text" x="85" y="435">0.1</text>
<text class="axis-text" x="85" y="385">0.2</text>
<text class="axis-text" x="85" y="335">0.3</text>
<text class="axis-text" x="85" y="285">0.4</text>
<text class="axis-text" x="85" y="235">0.5</text>
<text class="axis-text" x="85" y="185">0.6</text>
<text class="axis-text" x="85" y="135">0.7</text>
<text class="axis-text" x="85" y="85">0.8</text>
<rect x="520" y="120" width="150" height="60" fill="white" stroke="#333"/>
<line class="chart-line training-line" x1="530" y1="135" x2="560" y2="135"/>
<text class="text small-text" x="570" y="140">Training Loss</text>
<line class="chart-line validation-line" x1="530" y1="155" x2="560" y2="155"/>
<text class="text small-text" x="570" y="160">Validation Loss</text>
<rect x="520" y="200" width="150" height="80" fill="#f9f9f9" stroke="#333"/>
<text class="text small-text" x="595" y="220">Final Metrics:</text>
<text class="text small-text" x="595" y="240">Correlation: 0.944</text>
<text class="text small-text" x="595" y="255">MAE: 0.142</text>
<text class="text small-text" x="595" y="270">RMSE: 0.218</text>
</svg></body></html>