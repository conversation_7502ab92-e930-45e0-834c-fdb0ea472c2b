<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Figure 3</title>
<style>
body { font-family: 'Times New Roman', serif; margin: 20px; background: white; }
.figure-title { text-align: center; font-weight: bold; font-size: 14px; margin-bottom: 20px; }
.diagram { width: 100%; height: 600px; border: 1px solid #ddd; }
.box { fill: #e8f4fd; stroke: #2c5aa0; stroke-width: 2; rx: 8; }
.process-box { fill: #fff2cc; stroke: #d6b656; stroke-width: 2; rx: 8; }
.output-box { fill: #d5e8d4; stroke: #82b366; stroke-width: 2; rx: 8; }
.neural-box { fill: #f8cecc; stroke: #b85450; stroke-width: 2; rx: 8; }
.text { font-family: 'Times New Roman', serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; fill: #333; }
.small-text { font-size: 10px; }
.arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
</style></head><body>
<div class="figure-title">Figure 3. T5-based Correctness Assessment Workflow</div>
<svg class="diagram" viewBox="0 0 800 600">
<defs><marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
<polygon points="0 0, 10 3.5, 0 7" fill="#333" /></marker></defs>
<rect class="box" x="350" y="50" width="100" height="30"/>
<text class="text" x="400" y="65">Original Text</text>
<rect class="process-box" x="300" y="100" width="200" height="30"/>
<text class="text" x="400" y="115">Text Normalization & Tokenization</text>
<rect class="neural-box" x="150" y="170" width="150" height="60"/>
<text class="text" x="225" y="190">T5 Encoder</text>
<text class="text small-text" x="225" y="210">Contextualized</text>
<text class="text small-text" x="225" y="225">Representations</text>
<rect class="neural-box" x="500" y="170" width="150" height="60"/>
<text class="text" x="575" y="190">T5 Decoder</text>
<text class="text small-text" x="575" y="210">Error Correction</text>
<text class="text small-text" x="575" y="225">Generation</text>
<rect class="output-box" x="500" y="270" width="150" height="40"/>
<text class="text" x="575" y="290">Corrected Text</text>
<rect class="process-box" x="300" y="350" width="200" height="60"/>
<text class="text" x="400" y="370">Error Quantification</text>
<text class="text small-text" x="400" y="385">Token-level Comparison</text>
<text class="text small-text" x="400" y="400">Error Categorization</text>
<rect class="process-box" x="150" y="450" width="150" height="40"/>
<text class="text" x="225" y="470">Error Ratio</text>
<text class="text small-text" x="225" y="485">Corrections/Tokens</text>
<rect class="process-box" x="500" y="450" width="150" height="40"/>
<text class="text" x="575" y="470">Exponential Decay</text>
<text class="text small-text" x="575" y="485">100 × e^(-λ × ratio)</text>
<rect class="output-box" x="325" y="530" width="150" height="30"/>
<text class="text" x="400" y="545">Correctness Score</text>
<line class="arrow" x1="400" y1="80" x2="400" y2="100"/>
<line class="arrow" x1="350" y1="130" x2="225" y2="170"/>
<line class="arrow" x1="300" y1="200" x2="500" y2="200"/>
<line class="arrow" x1="575" y1="230" x2="575" y2="270"/>
<line class="arrow" x1="450" y1="130" x2="575" y2="170"/>
<line class="arrow" x1="500" y1="290" x2="450" y2="350"/>
<line class="arrow" x1="350" y1="130" x2="350" y2="350"/>
<line class="arrow" x1="350" y1="410" x2="225" y2="450"/>
<line class="arrow" x1="450" y1="410" x2="575" y2="450"/>
<line class="arrow" x1="300" y1="470" x2="350" y2="530"/>
<line class="arrow" x1="500" y1="470" x2="450" y2="530"/>
</svg></body></html>