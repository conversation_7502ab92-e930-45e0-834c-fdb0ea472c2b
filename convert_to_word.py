import re
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.shared import OxmlElement, qn

def create_word_document():
    # Create a new document
    doc = Document()
    
    # Set default font for the document
    style = doc.styles['Normal']
    font = style.font
    font.name = 'Times New Roman'
    font.size = Pt(12)
    
    # Read the methodology.txt file
    with open('methodology.txt', 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Split content into lines
    lines = content.split('\n')
    
    # Track if we're in references section and table processing
    in_references = False
    references = []
    in_table = False
    table_data = []
    current_table_title = ""

    i = 0
    while i < len(lines):
        line = lines[i].strip()

        # Skip empty lines in most cases
        if not line:
            if not in_references and not in_table:
                doc.add_paragraph()
            i += 1
            continue
        
        # Check if we've reached the References section
        if line == "References":
            in_references = True
            # Add References heading
            heading = doc.add_heading('References', level=1)
            heading_format = heading.runs[0].font
            heading_format.name = 'Times New Roman'
            heading_format.size = Pt(14)
            heading_format.bold = True
            i += 1
            continue

        # Check if we're starting a table
        if line.startswith('Table ') and not in_table:
            current_table_title = line
            in_table = True
            table_data = []
            i += 1
            continue

        # Process table content
        if in_table:
            if line.startswith('|') and '|' in line[1:]:
                # This is a table row
                row_data = [cell.strip() for cell in line.split('|')[1:-1]]  # Remove empty first and last elements
                table_data.append(row_data)
            elif line.startswith('|-') or line.startswith('|--'):
                # This is a separator line, skip it
                pass
            else:
                # End of table, create the table in Word
                if table_data:
                    create_word_table(doc, current_table_title, table_data)
                in_table = False
                table_data = []
                current_table_title = ""
                # Process this line normally
                continue
        
        # If we're in references section, collect references
        if in_references:
            if line and not line.startswith('References'):
                references.append(line)
            i += 1
            continue
        
        # Handle different types of content
        if is_main_heading(line):
            # Main headings (1., 2., 3., etc.)
            heading = doc.add_heading(line, level=1)
            heading_format = heading.runs[0].font
            heading_format.name = 'Times New Roman'
            heading_format.size = Pt(14)
            heading_format.bold = True

        elif is_sub_heading(line):
            # Sub headings (1.1, 2.1, etc.)
            heading = doc.add_heading(line, level=2)
            heading_format = heading.runs[0].font
            heading_format.name = 'Times New Roman'
            heading_format.size = Pt(14)
            heading_format.bold = True

        elif is_sub_sub_heading(line):
            # Sub-sub headings (1.1.1, 2.1.1, etc.)
            heading = doc.add_heading(line, level=3)
            heading_format = heading.runs[0].font
            heading_format.name = 'Times New Roman'
            heading_format.size = Pt(14)
            heading_format.bold = True

        elif line.startswith('Table ') or line.startswith('Figure '):
            # Table or Figure captions - skip these as they're handled in table processing
            pass

        elif line == 'Abstract' or line == 'Keywords:':
            # Special headings
            heading = doc.add_heading(line, level=1)
            heading_format = heading.runs[0].font
            heading_format.name = 'Times New Roman'
            heading_format.size = Pt(14)
            heading_format.bold = True

        else:
            # Regular paragraph text
            para = doc.add_paragraph()
            run = para.add_run(line)
            run.font.name = 'Times New Roman'
            run.font.size = Pt(12)
            para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY

        i += 1
    
    # Sort and add references alphabetically
    if references:
        # Clean and sort references alphabetically by first author's last name
        clean_references = []
        for ref in references:
            if ref.strip() and not ref.strip().startswith('References'):
                clean_references.append(ref.strip())

        # Sort alphabetically (case-insensitive)
        clean_references.sort(key=str.lower)

        for ref in clean_references:
            para = doc.add_paragraph()
            run = para.add_run(ref)
            run.font.name = 'Times New Roman'
            run.font.size = Pt(12)
            para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
            # Add hanging indent for references (APA style)
            para.paragraph_format.left_indent = Inches(0.5)
            para.paragraph_format.first_line_indent = Inches(-0.5)
            para.paragraph_format.space_after = Pt(0)  # No extra space between references
    
    # Save the document
    doc.save('Article_with_Tables.docx')
    print("Document saved as 'Article_with_Tables.docx'")

def create_word_table(doc, title, table_data):
    """Create a properly formatted table in Word document"""
    if not table_data:
        return

    # Add table title
    title_para = doc.add_paragraph()
    title_run = title_para.add_run(title)
    title_run.font.name = 'Times New Roman'
    title_run.font.size = Pt(12)
    title_run.font.bold = True
    title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # Create table
    table = doc.add_table(rows=len(table_data), cols=len(table_data[0]))
    table.alignment = WD_TABLE_ALIGNMENT.CENTER

    # Fill table data
    for row_idx, row_data in enumerate(table_data):
        for col_idx, cell_data in enumerate(row_data):
            cell = table.cell(row_idx, col_idx)
            cell.text = cell_data

            # Format cell text
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    run.font.name = 'Times New Roman'
                    run.font.size = Pt(12)

                # Make header row bold
                if row_idx == 0:
                    for run in paragraph.runs:
                        run.font.bold = True
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                else:
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # Add some space after table
    doc.add_paragraph()

def is_main_heading(line):
    """Check if line is a main heading (1., 2., 3., etc.)"""
    patterns = [
        r'^\d+\.\s+[A-Z]',  # 1. Introduction
        r'^Abstract$',
        r'^Keywords:',
        r'^References$',
        r'^\d+\.\s+Discussion',  # 6. Discussion
        r'^\d+\.\s+Conclusion'   # 7. Conclusion
    ]
    return any(re.match(pattern, line) for pattern in patterns)

def is_sub_heading(line):
    """Check if line is a sub heading (1.1, 2.1, etc.)"""
    patterns = [
        r'^\d+\.\d+\s+[A-Z]',  # 1.1 Subheading
    ]
    return any(re.match(pattern, line) for pattern in patterns)

def is_sub_sub_heading(line):
    """Check if line is a sub-sub heading (1.1.1, 2.1.1, etc.)"""
    patterns = [
        r'^\d+\.\d+\.\d+\s+[A-Z]',  # 1.1.1 Sub-subheading
    ]
    return any(re.match(pattern, line) for pattern in patterns)

if __name__ == "__main__":
    create_word_document()
