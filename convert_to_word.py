import re
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn

def create_word_document():
    # Create a new document
    doc = Document()
    
    # Set default font for the document
    style = doc.styles['Normal']
    font = style.font
    font.name = 'Times New Roman'
    font.size = Pt(12)
    
    # Read the methodology.txt file
    with open('methodology.txt', 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Split content into lines
    lines = content.split('\n')
    
    # Track if we're in references section
    in_references = False
    references = []
    
    for line in lines:
        line = line.strip()
        
        # Skip empty lines in most cases
        if not line:
            if not in_references:
                doc.add_paragraph()
            continue
        
        # Check if we've reached the References section
        if line == "References":
            in_references = True
            # Add References heading
            heading = doc.add_heading('References', level=1)
            heading_format = heading.runs[0].font
            heading_format.name = 'Times New Roman'
            heading_format.size = Pt(14)
            heading_format.bold = True
            continue
        
        # If we're in references section, collect references
        if in_references:
            if line and not line.startswith('References'):
                references.append(line)
            continue
        
        # Handle different types of content
        if is_main_heading(line):
            # Main headings (1., 2., 3., etc.)
            heading = doc.add_heading(line, level=1)
            heading_format = heading.runs[0].font
            heading_format.name = 'Times New Roman'
            heading_format.size = Pt(14)
            heading_format.bold = True
            
        elif is_sub_heading(line):
            # Sub headings (1.1, 2.1, etc.)
            heading = doc.add_heading(line, level=2)
            heading_format = heading.runs[0].font
            heading_format.name = 'Times New Roman'
            heading_format.size = Pt(14)
            heading_format.bold = True
            
        elif is_sub_sub_heading(line):
            # Sub-sub headings (1.1.1, 2.1.1, etc.)
            heading = doc.add_heading(line, level=3)
            heading_format = heading.runs[0].font
            heading_format.name = 'Times New Roman'
            heading_format.size = Pt(14)
            heading_format.bold = True
            
        elif line.startswith('Table ') or line.startswith('Figure '):
            # Table or Figure captions
            para = doc.add_paragraph()
            run = para.add_run(line)
            run.font.name = 'Times New Roman'
            run.font.size = Pt(12)
            run.font.bold = True
            para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
        elif line == 'Abstract' or line == 'Keywords:':
            # Special headings
            heading = doc.add_heading(line, level=1)
            heading_format = heading.runs[0].font
            heading_format.name = 'Times New Roman'
            heading_format.size = Pt(14)
            heading_format.bold = True
            
        else:
            # Regular paragraph text
            para = doc.add_paragraph()
            run = para.add_run(line)
            run.font.name = 'Times New Roman'
            run.font.size = Pt(12)
            para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
    
    # Sort and add references alphabetically
    if references:
        references.sort()
        for ref in references:
            if ref.strip():
                para = doc.add_paragraph()
                run = para.add_run(ref)
                run.font.name = 'Times New Roman'
                run.font.size = Pt(12)
                para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
                # Add hanging indent for references
                para.paragraph_format.left_indent = Inches(0.5)
                para.paragraph_format.first_line_indent = Inches(-0.5)
    
    # Save the document
    doc.save('Article.docx')
    print("Document saved as 'Article.docx'")

def is_main_heading(line):
    """Check if line is a main heading (1., 2., 3., etc.)"""
    patterns = [
        r'^\d+\.\s+[A-Z]',  # 1. Introduction
        r'^Abstract$',
        r'^Keywords:',
        r'^References$'
    ]
    return any(re.match(pattern, line) for pattern in patterns)

def is_sub_heading(line):
    """Check if line is a sub heading (1.1, 2.1, etc.)"""
    patterns = [
        r'^\d+\.\d+\s+[A-Z]',  # 1.1 Subheading
    ]
    return any(re.match(pattern, line) for pattern in patterns)

def is_sub_sub_heading(line):
    """Check if line is a sub-sub heading (1.1.1, 2.1.1, etc.)"""
    patterns = [
        r'^\d+\.\d+\.\d+\s+[A-Z]',  # 1.1.1 Sub-subheading
    ]
    return any(re.match(pattern, line) for pattern in patterns)

if __name__ == "__main__":
    create_word_document()
