Methodology
Grading Criteria
IELTS and TOEFL tests although adapt different writing scoring rubrics, they take a similar view on the properties of well-written essays. By and large, coherence, co- hesion, vocabulary usage, grammar usage, etc. are the major concerns in evaluating an IELTS or TOEFL essay. Nevertheless, in the routine teaching and evaluation of EFL writing, teachers pay more attention to the development of students’ writing ability in mechanics, grammar, organization, style, unity, vocabulary, content, etc. (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2014). Constrained by the limitations of the prior art in computer-assisted writing evaluation, the style-related essay assessment remains elusive. To this end, the evaluation categories adopted in this study involve unity, organization, fluency, content, vocabulary, grammar, and mechanics (see Table 1).
Table 1. Definitions of Evaluation Categories.

UNITY/COHERENCE	Relevance of Paragraphs to a Single clear central idea (Prompt),
relevance of Paragraphs to each other, transitions
 
ORGANIZATION/ COHESION
 
Paragraph development, paragraph structure, essay development, essay structure
 
FLUENCY	The clear, smooth use of language
CONTENT	Clear central idea, development of an idea
VOCABULARY	Proper word usage and word choice, use of different and complex
vocabulary
GRAMMAR	Correctness in tense, sentence structure, number, agreement,
article, preposition, pronoun, etc.; complexity in structure, phrasal structure
MECHANICS	Punctuation, spelling, capitalization, face, paragraphing, handwriting, space

 

According to the table above, in the proposed computer-assisted writing learning, we set five modules to execute the above five evaluation requirements for writing: THESIS, FLUENCY, CONTENT, COMPLEXITY, and CORRECTNESS. In
particular, “Thesis” evaluates the unity and organization, and sees the degree how the paragraphs serve the topic; “Complexity” consists of grammar and vocabulary as- sessments, it is a check of the authors’ mastery of morphology and syntax listed in the Table 1, meanwhile, “Correctness” checks the use of vocabulary, mechanics, and grammar conforming to the standard English. The schema of MsCAEWL’s evaluation criteria is shown in Figure 1.

Task Definition
In accordance with the requirements of writing feedback on the EFL writing teaching, this paper proposes a novel multi-strategy automatic computer-assisted EFL writing learning system MsCAEWL. The flow chart of the proposed system is shown in Figure 2.
The proposed system aims to solve the defects of the prior studies and proposes a series of solutions involving deep learning methods to give users detailed vocabulary and syntax modification or improvement suggestions and gives itemized and overall feedback and evaluations on compositions based on big data so that teachers and students can be fully aware of the writing process. MsCAEWL evaluates compositions

Figure 1. The mapping between evaluation modules of MsCAEWL and the evaluation categories of composition properties. The proposed computer-assisted EFL writing learning system consists of five main modules which function following the requirements of the general EFL writing evaluation categories, that is, unity, organization, content, fluency, vocabulary, grammar, and mechanics.
 

 
Figure 2. The flow chart of MsCAEWL. Essays and prompts are combined as inputs into MsCAEWL. The Theory, Fluency and Content modules generate the corresponding scores respectively. In the complexity module, sub-scores will be provided for each of the sentence and vocabulary levels, with the final completion score being driven by a combination of the two sub- scores. In addition to detecting and scoring syntactic and lexical errors, the Correction module also provides suggestions for error correction.

from five indicators of thesis (unity and organization), fluency, content, complexity (lexical and syntactic complexity), correctness (vocabulary, mechanics, and grammar), and incorporates the corresponding modules to conduct evaluations. Moreover, it diagnoses the errors of words, phrases, sentences, and improper use in essays, puts forward suggestions for correction, points out where words and sentences can be improved, and gives feedback. We proposed a model to describe the calculation of the evaluation process of MsCAEWL in equations (1)–(4), the notations are listed in Table 2.
FinalScore = 10αt ·St + 10αf ·Sf + 10αcon ·Scon + 10αl ·Sl + 10αs ·Ss + 10αc ·Sc
(1)
 
2 St 3	02 It  31
 
7 = δB6	7C


 
(2)
 
8>  02 It 31
 
'	It 3	'
 
>	B6	7C
 
4 6 S' 7 6 If  7	6 S' 7
 
>	B6
 
= 5, if
 
σ6 f 7 ≤ 6	7 ≤ σ 6 f 7
 
>	B6 Icon 7C

 
 
6 S'  7 6 Icon 7	6 S'  7

 	 	 
 
>	02 It 31
 
'	It 3	'
 
>	B6 If  7C
 
3 6 S' 7 6 If  7 4  6 S' 7
 
>	B6
 
= 4, if
 
σ6 f 7 ≤ 6	7 ≤ σ 6  f 7
 
>	B6 Icon 7C
 
6 S'  7 6 Icon 7	6 S'  7
 
@4 Is 5A
>	02	31
 
con
'
s
 
4 Is 5
 
con
'
s
 
It
>	B6	7C
 
'	It 3	'
 
>< B6	7C
 
2 6 S' 7 6 If  7 3  6 S' 7
 
= 3, if
 
σ6 f 7 ≤ 6	7 ≤ σ 6  f 7
 
(3)
 
>	B6 Icon 7C

 
 
6 S'  7 6 Icon 7	6 S'  7

 	 	 
 
>	02 It 31
 
'	It 3	'
 
>	B6	7C
 
1 6 S' 7 6 If  7 2  6 S' 7
 
>	B6
 
= 2, if
 
σ6 f 7 ≤ 6	7 ≤ σ 6  f 7
 
>	B6 Icon 7C

 
 
6 S'  7 6 Icon 7	6 S'  7

 	 	 
 
>	02 It 31
 
2 It 3	'
 
>	B6 If  7C
 
6 If  7 1  6 S' 7
 
>	δB6
 
7C = 1, if 6
 
7 ≤ σ 6  f 7
 
>	B6 Icon 7C


 
6 Icon 7


 
6 S'  7


 
σ (x	  1	 1 + e
 
(4)
 

In the proposed model, each evaluation module possesses different approaches to modeling and calculating in scoring compositions, the ranges of scores of the Thesis, Fluency, Content, and Syntactic Complexity modules vary, thus the functions are implemented to normalize and scale the above sub-evaluation sores. The logistic
 

Table 2. Notations.

St	Score of the Thesis evaluation
Sf	Score of the fluency evaluation
Scon	Score of the content evaluation
Sl	Score of the lexical complexity evaluation
Ss	Score of the syntactic complexity evaluation
Sc	Score of the correctness evaluation
αt	Coefficient of the thesis evaluation
αf	Coefficient of the fluency evaluation
αcon	Coefficient of the content evaluation
αl	Coefficient of the lexical complexity evaluation
αs	Coefficient of the syntactic complexity evaluation
αc	Coefficient of the correctness evaluation
σ(x)	Logistic sigmoid function
δ(x)	The normalizing and scaling function
It , If , Icon Is	The intermediate values to scale values of S' , S' , S' S'
t  f	con s




sigmoid function σ(x) is introduced to normalize the scores into [0, 1], then δ(x) divides the sigmoid result of scores of the Thesis, Fluency, Content, Syntactic Complexity
modules into five levels based on the mean and assigns {1, 2, 3, 4, 5} credits re- spectively to each level.
Considering the value ranges of the scoring modules still differ (see Table 3), coefficients are introduced to regulate the individual score of each module and the total score. αt, αf , αcon, αl, αs, αc are leveraged as the coefficients adjustable according to the upper limit of the total score or the focus of the writing evaluation. In our study, we set a scenario where the total score of composition is 100 credits, the thesis, fluency, and content aspects of writing are paid more attention in evaluation and scoring. Thus, the score assignment and coefficients are set as shown in Table 4. Follow the settings, the total score turns out 100 credits calculated as equation (1). All the above settings and the upper limit of the total score can be adjusted according to requirements, for instance, the maximum total score could be 10 credits along with the corresponding parameter settings.

The Thesis Module
The majority of EFL writing exams contain opinion/discussion writing tasks essentially with prompts in them. The writing prompt students must respond to in the writing task of EFL is a compact set of writing instructions that assist students to focus on a certain topic, task, or goal. Writing prompt is the key to achieving Unity.
 

Table 3. Value Range of the Scoring Modules or Functions.

Function or score	Range
 
2 St 3	02 It 31
 
{1, 2, 3, 4, 5}
 
7 = δB6	7C

Sl	[0, 2]
Sc	[0, 1]
σ(It), σ(If ), σ(Icon)σ(Is)	(0, 1)

Table 4. The Score Assignment and Coefficient Setting.

Complexity

	
Thesis	
Fluency	
Content	Lexical Complexity	Syntactic Complexity	
Correctness
Score	20	15	30	10	10	15
Coefficient (α)	0.4	0.3	0.6	0.5	0.2	1.5


Quality of Organization is related to whether ideas are arranged logically and accurately. Each part of the essay is supposed to be clearly organized and flow smoothly. An essay with good organization indicates that paragraphs should support the thesis of the paper.
However, the EFL students often fail to grasp the topic or have problems in de- veloping content with regard to the prompt, thus possibly resulting in digression or failure to arrange the ideas or statements to support the thesis. Due to incapacity in identifying the content of texts, the prior AWE models do not attend to this issue and are inadequate to address it. Much worse, students tend to cheat models with fancy ex- pressions and words while leveraging automatic writing evaluation means, which is inevitable given the current state of the art.
This module is incorporated to check the accordance between the prompt and the main body, and the unity of each paragraph, i.e., the Thesis module merges the Unity and Organization evaluation. A traditional wide-applied Natural Language Pro- cessing method is implemented to accomplish this end. TF–IDF, short for Term Frequency–Inverse Document Frequency, is a numerical statistic to qualify how im- portant or relevant a word/words is to a set of a document amongst a collection of documents. It is often used as a weighting factor in searches for information retrieval, text mining, and user modeling. TF looks at the frequency of a particular term (word) in a document, while IDF - the commonness of the term in a corpus. In this case, All The News corpus from Kaggle is incorporated (see section “Datasets”). TF-IDF seeks the
 

importance of the term inversely related to its frequency in a corpus and is calculated as follows.
TF — IDF(word, document, corpus)= TF(word, document)·IDF(word, corpus)
(5)
 
where

TF(word, document
 

number of occurrences of the word in the document
)=	total number of words in the document
 


(6)
 
IDF(word, corpus)= log	total number of documents
number of documents with the word in it
 

	(7)
 
In the Thesis evaluation task, two word vectors are calculated by TF-IDF utilizing two word sets containing the keywords and excluding the Stop words respectively from the prompt paragraphs and the composition, only the synonyms of the prompt word set are added after selection. The cosine similarity of these two vectors which are the semantic representations of the prompt, and the composition is gained to measure the degree of the Thesis between the prompt and the essay. The larger the value, the more relevant the two are, and the higher the Thesis degree is. The calculation is shown in equation (8).
	·V	
 
S' = Sim V , V
 
 = cos (θ)=	Vp   com
 
(8)
 
t	p	com
 
¨V ¨ × V	 
 
where, S' is the score of the Thesis module, Sim () is the cosine similarity function,
Vp is prompt vector = IDF (prompt), Vcom is compositon vector = IDF (composition).

The Fluency Module
Writing Fluency refers to a student’s ability to write with a natural flow and rhythm, utilizing appropriate word patterns, vocabulary, and content. Syntactic fluency refers to the extent to which a writer constructs a sentence containing linguistically complex structures (Shapiro, 1999). More specifically, fluent composition flows smoothly from word to word, phrase to phrase, and sentence to sentence. In Natural Language Processing the N-gram-based metrics carry a similar function achieving the above requirement for Writing Fluency.
As an automatic evaluation metric of text generation or machine translation BLEU (Bilingual Evaluation Understudy) measures the closeness by comparing the machine output against the human reference and is well-known for its strong correlation with human evaluation in adequacy and fluency. Its calculation is as follows:
 
BLEU = BP · exp
 

N

n=1
 
wnlogpn!
 

(9)
 
BP = 8<:
 

1
 1 — r 
 

if c > r if c ≤ r
 


(10)
 

where pn is the precision of n — gram, c is the length of the system output, and r is the length of the reference corpus, BP indicates the brevity penalty.
BLEU compares the n-gram of the candidate with the reference corpus to count the number of overlaps that are independent of the word positions. This n-gram precision scoring captures two aspects of translation: adequacy and fluency. The longer n-gram overlaps between the candidate and reference account for fluency, which means the generated sentences are well-formed and mature in length and structure compared with the reference.
In this study, BLEU with 4-g is utilized for writing fluency evaluation matric. We take compositions as system output candidates in machine translation, and a standard corpus with sufficient capacity is used as a reference. Considering that EFL writing requires writing with standard English, we choose a news set as the reference corpus. The Fluency score is the average BLEU score of each sentence from the essay against the news corpus (All The News, see section “Datasets”). The calculation is shown in equation (11).
 
n
Bleui
S' = i=1	
 


(11)
 
f	n
where, S' is the score of the Fluency module, Bleu indicates the BLEU score of the
i
i — th sentence, n is the total number of the sentences of the corresponding essay.

The Content Module
In essence, grading the content of writing is a comprehensive evaluation of whether the thesis and purpose are clear, whether expectations and development are in a consis- tently excellent manner according to the viewpoint, whether the structure and word choice and use are fluent and graceful, and so on. Content evaluation is the core of the human judgment of an essay; thus, it regularly presents as holistic scoring.
The AWE/AES models assign holistic scores based on the writing content. More than 90% of AES models are trained and tested leveraging the ASAP (The Automated Student Assessment Prize) (see section “Comparison experiments”) corpus and its scoring labels. Based on the ASAP corpus as well, this article established an LSTM- based model to assess the writing content. Recurrent Neural Network (RNN) has been widely applied in various NLP tasks for their outstanding time series feature extraction capability. Long-short-term memory (LSTM) (Hochreiter & Schmidhuber, 1997) (the formula is as follows) is an improvement to solve the problems such as the disap- pearance of the RNN gradient. LSTM is introduced in this section to extract sequential
 

features from essays with different scores in the training set so as to predict the holistic score of the essays to be tested.
In an LSTM cell, the hidden state is split into two vectors: ht and ct , which re- spectively represent the short-term state and the long-term state. The current input vector xt and the previous short-term state ht—1 are fed to four different fully connected
layers. One layer outputs c analyzing the current inputs x and the previous (short-term)
bt	t
state ht—1. The other three are gate controllers. They control information by activation functions, whose range is from 0 to 1. The forget gate ft determines what proportion of
the previous long-term state ct—1 should be erased. The input gate it governs to what
extent ct should be added to the current long-term state ct. Finally, the output gate ot
score of the Content module S'	is the output Y	of LSTM networks, that is the rating
pred
of a whole essay. The processing progress is depicted in equations (12) and (13).
8> 2 it 3	2 σ  3
>	7 = 6	7W ·[h	, x ]
ct	tanh
>	
c = f 1 c	+ i 1 c
>:	ht = ot 1 tanh(ct)
 
'
con
 
= Ypred	(13)
 

The Complexity Module
As stated in section “Grading Criteria.” Complexity checks the lexical and structural diversity and complexity, i.e., the proficiency in vocabulary and grammar of writers. Two sub-modules are incorporated to achieve this end: the Lexical Complexity and the Syntactic Complexity.

The Lexical Complexity. The existing AWE research and products give improving feedback on vocabulary choice or usage by listing the possible synonyms of words or phrases in the essays, merely based on dictionary-matching technology. The listed words or phrases will not be changed according to the learners’ level. Most students can’t benefit from feedback and learn new-to-them vocabulary and usage. Thus, the word list scales based on the EFL learners’ lexical knowledge are introduced to address this problem. The New JACET 8000 (Committee, 2016) is the scaled list of basic words established by the Japan Association of College English Teachers (JACET). Eight levels are divided among 8000 words conforming to the lexical profile of Japanese EFL learners, and each level contains 1000 words. On the basis of the author’s vocabulary
 

level being positioned leveraging the word scales, the gap between the author’s level and the writing requirements is judged and corresponding evaluation and suggestions on vocabulary use are given accordingly.
According to the learners’ level, a higher vocabulary set and a lower vocabulary set are established. The words or phrases in the composition are replaced by the synonyms from the higher and lower vocabulary sets respectively using the thesaurus. If the replacement occurs in the higher vocabulary set, it indicates that the author still has room for improvement in the use of this replaceable word or phrase. The system will output the words in the higher vocabulary set as improving suggestions, and at the same time reduce the score of Lexical Complexity. On the contrary, the replacement can only be found in the lower vocabulary set, the system will output no suggestions, and the score is increased.
A novel model for scoring lexical complexity is proposed in this study. We use the ratio of the total number of edits that can be replaced to the weighted total number of words after deduplication as the basis for the vocabulary complexity score. The calculation is as follows:
S =	1 +  f (R) 	(14)
l	C × a%
 
8<:
 
f (R)= C × a%, if R ≥ C × a%
f (R)= R, if — C × a% ≤ R ≤ C × a%
f (R)= —C × a%, if R ≤ — C × a%
 


(15)
 
R = P + N	(16)
where Sl is the final score of the Lexical Complexity, P is a positive value, representing the number of bonus credits, N is a negative value and represents the number of penalty credits, R is the total number of the replaceable edits, i.e., the sum of P and N , C denotes the count of the words after deduplication. Note that a functions as an adjustable
parameter and represents the word number. The weight a% specifies the limit that the system allows for rewards or penalties for lexical complexity. We take a = 30 as the threshold, that is, the bonus credits greater than or equal to 30% of the composition words will reach the upper limit of the reward, and the penalty credits less than or equal to 30% of the composition words will reach the upper limit of the penalty. The range of
Sl is from 0 to 2. When R exceeds the reward upper limit,  f (R)  = 1, i.e., Sl equals 2, which means the proficiency in vocabulary mastery of this composition is excellent. When R exceeds the penalty upper limit,  f (R)  = —1, then, Sl equals 0, which means
the vocabulary mastery of this composition does not meet the requirements.

The syntactic complexity. The Syntactic Complexity mainly depends on the diversity of structural patterns and the flexible application of the grammar, such as the diversity of active and passive sentences, clauses, participles, etc. From the linguistic point of view,
 

these syntactic phenomena have their own characteristic function words. These function words are selected into a set, such as: which, when, copular verbs, adverbs, etc. TextRank in NLP is incorporated to calculate the representation of function words in the set based on lexical relations, i.e., the appropriateness of the syntax use represented by the function-word-centered lexical relationship. The TextRank values based on function words in the news corpus (All The News, corpus from Kaggle in section “Datasets”) and each composition are calculated and compared respectively. The higher the similarity between the two, the better the Syntactic Complexity, and the higher the score. The Syntactic Complexity is calculated based on a real-world corpus, there are thus no worries about being cheated by adversarial samples.
TextRank, as an application of graph algorithm, takes words of text as graph nodes, constructs a graph model, and then calculates the importance of each word based on the weights between words. That is, the ratio of the weight w{ji} of the ( j, i) edge formed by word i between its frontal each point (word) j to the sum of the weights of the point j to the other edges is the TextRank value S(vi) of the word, and the calculation is shown in
equation (17).
 
S(vi)= (1 — d)+ d
( j, i)∈ε
 
w{ji}
vk ∈out(vj )
 

wjk
 

S vj	(17)
 
The Syntactic Complexity score is the average TextRank score of each sentence from the essay. The calculation is shown in equation (18).
 
n
Tri
S' = i=1	
 


(18)
 
s	n
where, S' is the score of the Syntactic Complexity module, Tr denotes the TextRank
sore of the i — th sentence, n is the total number of the sentences of the corresponding essay.

The Correctness Module
As stated in section “Grading Criteria,” Correctness checks the proper use of vo- cabulary, mechanics, and grammar, i.e., whether the compositions are written in correct standard English. Two sub-modules are incorporated to achieve this goal: the GED module locates the lexical or syntactic errors, and the GEC module corrects them and outputs the correction suggestions.

The Grammatical Error Diagnosis module. Grammar error detection (GED) can be an- alyzed as a Sequence Tagging task, that is, the task of predicting labels based on sequential features. Although BERT and Transformer techniques are popular in all aspects of NLP, the RNN family is more dominant in the extraction of sequential
 

features. Therefore, this study adopts Bi-directional Long-short term memory neural networks (Bi-LSTM) to detect grammatical errors.
A large number of experimental studies and applications have proved that the BERT model pre-trained based on a huge multilingual corpus can obviously improve the effect of various downstream tasks because of its excellent semantic/syntactic rep- resentation ability (Devlin et al., 2019; Herzig et al., 2020; Sun et al., 2019; Zhu et al., 2019). In this study, BERT’s pre-training strategy is implemented to Fine-tune the training data, and then the task of grammar error detection is carried out.
After the text enters the BERT model for fine-tuning, a new semantic vector representation is obtained, which is then processed by Bi-LSTM and conditional random field (CRF) to finally output the error type tags. The calculation process is shown in Figure 3:

The Grammatical Error Correction module. Grammatical Error Correction models with SOTA scores mostly regard Grammatical Error Correction as a machine translation task and often adopt an Encoder-decoder structure. In this study, a novel Grammatical Error Correction architecture is inspired by the previous study (B. Chen & J. Zhang, 2022). As the neural networks in the Grammatical Error Diagnosis module, the training text obtains a new semantic representation after pre-training via BERT, and then enters the Encoder-decoder model with Global attention, where the Encoder involves a

Figure 3. The diagram of the Grammatical Error Diagnosis model. This vector representation is used to make predictions regarding the type of errors within the text, utilizing both Bi-LSTM and CRF for the task. The BERT model is fine-tuned with the text, resulting in a new semantic vector representation. This vector is then processed with Bi-LSTM and CRF, ultimately outputting the error type tags.
 

uni-direction LSTM, and the Decoder takes Bi-LSTM as the core. The specific ar- chitecture is depicted in Figure 4.

The scoring method of the correctness evaluation. The Grammatical Error Diagnosis module charges for identifying and showing the errors to the users, meanwhile, the Grammatical Error Correction module is responsible for correcting the located errors. The errors located and corrected are supposed to be marked as the candidate items of losing credits. The mechanic of penalty credits for grammatical errors is depicted in equations (19) and (20).
 
γ(a% × C — e)
 
Sc =	a% × C
 

(19)
 
γ(x)= ReLU (x)= max (0, x)	(20)
where, Sc is the score of the Correctness module, e denotes the total number of errors, C denotes the word count of the essay after deduplication. ReLU , the Rectified linear unit function, is a piecewise linear function that outputs any input value to zero if it is
negative, otherwise, outputs input to itself. a is the adjustable threshold that controls the maximum number of errors that can be tolerated. a = 20 is adopted in our study, which means Sc turns out 0 credits if the error count exceeds 20% of the whole composition under the control of ReLU function. The range of Sc is from 0 to 1, where Sc equals one when e equals 0, that is, there are no errors in the essay.



Figure 4. The Architecture of Grammatical Error Correction Model. In the Grammatical Error Diagnosis module, the training text is pre-trained using BERT to obtain a new semantic representation, which is then fed into the Encoder-Decoder model with Global Attention. For the Encoder, a uni-directional LSTM is employed. The Decoder, on the other hand, utilizes a Bi- LSTM as its central component.
 
Experiments
Datasets
a)	Reference corpus in the model

In sections of the Thesis, Fluency module, and the Syntactic Complexity of the Complexity module, the role of the reference corpus is essential. The quantity and quality of the corpus will lead to a change in scores, making the scores unreliable, which means a massive and error-free corpus is required. Most EFL writing tasks focus on elaborating or discussing opinions and examining the author’s proficiency in standard English. Therefore, the writing style requires written language rather than spoken language. In view of this, the news texts with rigorous terms are adopted as the basic dataset.
The All The News1 from Kaggle consists of 143,000 news articles covering a wide range of topics from 15 American publications including the New York Times, Breitbart, CNN, Business Insider, etc. This reference corpus derives from the news from 2016 to 2017, with a total size of about 1.2 Gb.
b)	The         scored         essay         corpus The ASAP corpus is the dataset incorporated in the Kaggle (2012) AES competition,
the Automated Student Assessment Prize. The corpus has a total of 12,976 compo-
sitions under eight prompts, the distribution of the essays under prompts and the score range from human raters are shown in Table 5. The score range varies. Furthermore, it is worth noting that there are one or more human raters on individual composition, and the final scores are determined or calculated in different ways according to the corre- sponding prompt. The comparison experiments of most AES/AWE studies are con- ducted based on the final resolved score, and so do the experiments in this study.

Comparison Experiments
Evaluation metric. Quadratic Weighted Kappa (QWK) which is the official metric of the Automated Student Assessment Prize and regarded as the major metric of the per- formance of AWE models is introduced as the evaluation metric in this section. It measures the agreement between the prediction score set and the label score set. The QWK value ranges from 0 to 1, which indicates the agreement arises from arbitrariness to completeness. In this sense, the human annotator rating set is considered as the labels, and the system outputs present as the prediction scores.
An N-by-N quadratic weight matrix W is first computed to encode the rating information.
 

Table 5. The Statistics of the ASAP Corpus.

Essay Type	Essays	Score Range	Avg words	Avg sentences
Prompt 1	Argumentative	1783	2–12	350	23
Prompt 2	Argumentative	1800	1–6	350	20
Prompt 3	Source-dependent	1726	0–3	150	6
Prompt 4	Source-dependent	1772	0–3	150	4
Prompt 5	Source-dependent	1805	0–4	150	7
Prompt 6	Source-dependent	1800	0–4	150	8
Prompt 7	Narrative	1569	0–30	250	12
Prompt 8	Narrative	732	0–60	650	35
		

Wi, j =	
(i — j)2		
(21)
(N — 1)2
where N is the number of possible ratings. An N-by-N matrix A is calculated such that Ai, j corresponds to the number of essays that receive a score i by the human rater, and a score j by the scoring system. Another N-by-N matrix B is constructed as the outer product of the histogram vectors of the two ratings. A and B are then normalized such that they have the same sum. Finally, from the three matrices, the quadratic weighted kappa is calculated as follows:
 

k = 1 —	i, jWi, jAi, j
i, jW(i, j)Bi, j
 

(22)
 

Comparison based on holistic scoring. Most AWE models adopt a holistic scoring method, i.e., an AWE model outputs an overall score of the corresponding essay, which is also an essential part of the scoring system of our proposed model. In order to validate the performance on holistic scoring of the proposed model, frontier models in AES/AWE (Table 6) are introduced as baseline models compared with MsCAEWL.
ASAP corpus with holistic scores as training labels serves as experiment data set in this section. Although more than 10 years have passed since the Kaggle AES com- petition, the labeled data set continue to benefit the AWE/AES field. Remarkable models have been emerging, especially for ones with neural networks incorporated, long after the competition event.
However, there are two issues that do not fit the requirements of our experiment and need to be modified in the pre-processing.
First, the number of human annotators in each essay is not consistent. Most essays contain two rater scores while some of them have three rater scores. More, given that one set of essays adopts trait scoring evaluation, i.e., the analytic scoring, while the others incorporate the holistic scoring method, the resolved score is obtained
 

Table 6. The Descriptions of the Baseline Models.

Baseline models	Sources	Backbone

S1	RL1 (Wang et al., 2018)
BERT, LSTM, boosting tree
S2	TSLF (Liu et al., 2019)
Dilated LSTM
S3	BLRR (Phandi et al., 2015)
EASE, BLRR, SVM
S4	LSTM-CNN-att (Dong et al., 2017)
LSTM, CNN, attention
S5	SKIPFLOW LSTM (Tay et al., 2018)
LSTM
S6	BERT + Essay-level features (Uto et al., 2020)
BERT


inconsistently: either the higher rater score is directly elected, or the composite score is calculated by relevant equations. The score range of each prompt thus differs. In this section, we take the resolved scores as baseline scores or labels regardless of the acquiring method. Before experiments, the score range should be normalized into (0, 1).
Second, out of privacy protection, the competition organizer has removed any personal information and replaced the relevant entities with marks, such as @OR- GANIZATION1, @PERSON1, etc. It is worth noting that the replacements often act as vital sentence constituents like subjects or objects. For people and machines, this is obviously a huge obstacle to reading or understanding, especially for machine learning methods with semantic-based models as their backbones. Since all the modules of the proposed model are highly semantic-dependent, these marks will infect the perfor- mance to a different extent. Hence, we randomly choose words under the marked categories to restore the sentence, such as replacing @ORGANIZATION1 with “bank” and @PERSON1 with “Jackson.”
In the comparison experiments, the resolved scores of human raters are utilized to calculate the QWK values of all the baseline models and MsCAEWL under each prompt, and the average QWK value of all prompts from the corresponding model is presented as well. All results are shown in Table 7, the value in the bolt indicates the optimal performance in the corresponding comparison.
The comparison results show that MsCAEWL gains the five top accomplishments out of eight prompts and the optimal performance in the track of average QWK score. On the whole, S6, S2, and S4 have achieved relatively satisfactory in part of the tracks. From their experimental results and the model architecture, it can be concluded that the sentiment-oriented structures based on the cutting-edge language models greatly boost the experimental results, such as LSTM, BERT, and attention mechanism. Though the S6 obtains astonishing results in most prompts, the two obvious weakness items occur in prompts 2 and 8 which contain the biggest average length of the essay, 350 words. However, in these two tracks, the top models including ours are based on RNN serial models, namely, LSTM. That is, the RNN architecture remains the superior ability to
 

Table 7. The Comparison with the Baseline Models on Holistic Scoring.

	S1	S2	S3	S4	S5	S6	MsCAEWL
Prompt 1	0.766	0.852	0.761	0.822	0.832	0.852	0.889
Prompt 2	0.659	0.736	0.606	0.682	0.684	0.651	0.781
Prompt 3	0.688	0.731	0.621	0.672	0.695	0.804	0.815
Prompt 4	0.778	0.801	0.742	0.814	0.788	0.888	0.831
Prompt 5	0.805	0.823	0.784	0.803	0.815	0.885	0.873
Prompt 6	0.791	0.792	0.775	0.811	0.810	0.817	0.821
Prompt 7	0.76	0.762	0.730	0.801	0.800	0.864	0.834
Prompt 8	0.545	0.684	0.617	0.705	0.697	0.645	0.713
Avg	0.724	0.773	0.705	0.764	0.764	0.801	0.820

capture sequential semantic features, especially in a long text, despite the fact that S6 particularly adapts the BERT to fit the large-scale text mission.
Owning to the multiple strategies incorporated in NN, the proposed system can benefit from the advantages of various technologies so as to meet different challenges. There is an obvious superiority in tackling long text, the proposed model dominates in almost all essay sets with more than 150 words size, which mainly leads to the su- periority in average score.

Comparison based on the analytic scoring. In this section, we aim to verify the analytic scoring of the proposed system. Hence the dataset with trait-specific scores as labels is primary to the validation experiments. Though the ASAP corpus provides the trait- specific scores on part of sets, there are two obstacles making the dataset unsuitable for the experiments. Only two prompts are presenting the analytic scores, prompts 7 and 8. However, the trait categories differ from each other. The scoring of prompt 7 evaluates Ideas, Organization, Style, and Conventions, while Ideas and Content, Organization, Voice, Word Choice, Sentence Fluency, and Conventions involves in the assessment of prompt 8. That means, the evaluation rubrics of each prompt are different, and the focus on parts of writing ability from the examiners are different. Moreover, the compositing methods of total scores are distinct. The final total score on each essay is simply obtained by adding 2 raters’ sum scores of all traits in prompt 7, rather than the weighted sum of scores on each trait by 2 taters in prompt 8. Since the traits and compositing methods of MsCAEWL are not consistent with any prompts above, it reveals unlikely to be utilized as the basis of the analytic scoring comparison.
By all accounts, a new validation experiment is set as follows. A group of college students is asked to write an essay according to a prompt. The essays then are scored by human raters and MsAWE as well following the detailed analytic scoring instructions concretely categorized as Thesis, Fluency, Content, Complexity, and Correctness. Accordingly, the trait-specific and total scores between human and machine raters are
 

obtained eventually to be leveraged to reveal the analytic scoring ability of the proposed writing computer-assisted writing learning by statistical analysis.
In this experiment, 80 sophomores majoring in English are invited as subjects, i.e., 80 compositions will be produced. Three experienced college teachers of English language and literature review and score each essay so that every essay contains three score sets with six scores. The writing prompt (see Table 8) is selected from the Tests for English Majors Grade Four (TEM 4) which is specialized for evaluating the com- prehensive EFL ability of English majors in China.
The scoring rubrics, coefficient settings, score scope, and the score compositing equations in this experiment are shown in section “Task Definition,” which are mainly derived from the grading criteria of TEMS. The statistical results of trait and total scores from human and machine raters are shown in Table 9.
The box-plot diagrams in Figure 5 depict the distribution of rating scores of MsCAEWL and human annotators. It can be seen that, overall, the machine rating keeps constituent with the average rating of three human raters, which is also consistent with the QWK results in section “Comparison based on holistic scoring.” That is, MsCAEWL is reliable in scoring in comparison with the overall human level.
It should be noted that the data distribution of MsCAEWL ratings is significantly less volatile than that of the human ratings. More important, human ratings vary widely from one another, and it is difficult to maintain consistency under each category. The opinions of human raters keep the relative agreement in thesis and correctness. It shows that human raters are easier to reach an agreement at judging digression and quality of organization. Furthermore, the review of grammar, spelling, mechanics, and other errors is more objective, and human judges are less likely to disagree on such issues. However, for evaluations that need multi-dimensional consideration, such as fluency, content, and complexity, human ratings are prone to diverge. To recap, the scoring of MsCAEWL is more robust.


Table 8. Writing Prompt From TEM 4.

Prompt	Source
 
Read carefully the following excerpt on term-time holiday arguments in the UK, and then write your response in NO LESS THAN 200 words, in which you should
1)	Summarize the main message of the excerpt, and then
2)	Comment on whether parents should take children out of school for a holiday during term time in order to save money
You should support yourself with information from the excerpt
Marks will be awarded for content relevance, content sufficiency, organization, and language quality. Failure to follow the above instructions may result in a loss of marks
*Specific source reading material is omitted here
 
TEM-4 of 2016
 

 
 










Table 9. The Statistical Results of Trait and Total Scores From Human Raters and MsCAEWL.

Thesis	Fluency	Content	Complexity	Correctness	Total

	Mean	Std		Mean	Std		Mean	Std		Mean	Std		Mean	Std		Mean	Std
R1	14.10	1.29		10.43	0.84		20.25	1.54		13.89	1.17		11.23	0.85		69.90	5.71
R2	13.74	1.39		9.43	0.81		21.84	1.62		12.10	1.23		10.93	0.81		68.02	5.85
R3	14.47	1.18		9.93	0.80		21.05	1.61		12.98	1.09		11.02	0.76		69.45	5.43
Avg	14.10	1.33		9.93	0.91		21.04	1.72		12.99	1.37		11.05	0.82		69.16	5.35
MsCAEWL	13.84	0.86		10.21	0.70		20.40	1.18		13.42	0.75		11.37	0.53		69.25	4.02
 

 
Figure 5. Box-plot diagram of score distribution by MsCAEWL and human raters. The box-plot diagrams show the distribution of rating scores of MsCAEWL and human annotators.
Comparison of the diagrams indicates that the machine rating is consistent with the average rating of three human raters. This implies that MsCAEWL is reliable for scoring in relation to the overall human level.
 
Validation of effects on writing learning assistance of MsCAEWL
The aforementioned experiments can only compare model performance based on their scores. Our model may offer not only scores, but also feedback such as im- provements in vocabulary and grammar, error correction, and so on. Scores are solely used as a reference for validation of improvements in writing ability, the major priority of the proposed system is to enhance students’ writing skills. By that means, in this part, we devised experiments to verify if MsCAEWL can increase students’ writing abilities.

Method. In this section, 70 high school sophomores with comparable scores in a high school in Guizhou province were selected as subjects for a 60-day writing learning experiment. Among them, 35 were randomly assigned to the experimental group and 35 to the control group. There was no significant difference in writing proficiency between the two groups. The experimental group and the control group received ordinary face-to-face writing instructions from the same teacher during the experiment. Differently, in the experimental group, the MsCAEWL is adopted to assist writing learning. According to the writing feedback by MsCAEWL, the experimental group students revise their essays by themselves. A pre-test was performed before the experiment, and a post-test was performed after the exper- iment for the experimental and the control group. The performances of the two groups are analyzed by independent-sample t-test and paired-sample t-test with SPSS 26.0 to verify the effects of MsCAEWL in improving students’ English writing proficiency. All compositions in the experimental and control group were scored by MsCAEWL.
Participants. Participants (n = 70) were high school sophomores from the same school in Guizhou province, China, aged 16–17 years. All participants take eight English lessons per week, each lesson lasts 45 minutes according to their school schedule. There were 35 participants in the experimental group (male = 20, female = 15), and
35 participants in the control group (male = 16, female = 19). The two groups took a
writing test before the writing instruction experiment. There was no significant dif-
ference in writing proficiency between the two groups. The average scores of par- ticipants between the control and experimental group in the pre-test are comparable:
62.32 (SD 6.99) and 61.68 (SD 5.85), respectively.

Materials. In the writing learning experiments, several essay prompts from the former National Matriculation English Tests are adopted as the writing learning and teaching materials. Given the short period of the verification experiment, the familiar materials
 
are more beneficial for students to achieve better writing performance. Moreover, the prompt design of the National Matriculation English Tests is more rigorous, and the difficulty of each prompt varies slightly, so it is more suitable for the experiment of this study. The prompts are selected randomly as follows:

Procedure. Before the experiment, the experimental and the control group were pre- tested with the first writing prompt to determine whether there was a difference in writing proficiency between them. At the end of the experiment, the two groups were post-tested using the fourth prompt. During the experiment, two groups of students received four writing instructions from the same English teacher, with an interval of 20 days. The wring prompts are shown in Table 10. Teachers’ guidance, explanation of relevant vocabulary and sentence patterns, and thesis discussion are arranged in each instruction. In most scenarios of EFL lessons in China, there is merely no time for exclusive writing teaching and learning due to the large number of students and the heavy workload. Consequently, writing teaching could only be an adjunct to the explanation of the text and the test questions. The abundant essays after assignments make teachers have little time to review and issue detailed feedback. Commonly, a holistic score with a short comment is attached to each essay after the short-term review.



Table 10. Prompts Adopted in Writing Learning.

Round	Prompt	Sours
 
First: Pre- test
 
Assuming you are Li Hua, and you would like to invite Henry, a foreign teacher, to visit the Chinese paper-cutting art exhibition. Please write him a letter, which includes: (1) Exhibition time and place; (2) Exhibition content
 
National test II of 2017
 
Second	Assuming that you are Li Hua, your New Zealand friend terry will visit a Chinese friend’s home and send an email to ask you about the customs. Please reply to the email, which includes: (1) arrival time; (2) appropriate gifts; (3) table manners
Third	Last weekend, you and your classmates took part in a picking activity. Please write a short essay for the class English corner to introduce this activity, including (1) farm activities; (2) the picking process; (3) personal feelings
 
National test I of 2018



National test II of 2020
 
Fourth: Post- test
 
Assuming you are Li Hua, and your English friend peter wrote to ask about the students’ sports in your school. Please write back to him, including (1) sports venues in the school;
(2) main kinds of sports; (3). Your favorite sports
 
National test III of 2018
 

 
 
Thus, in order to simulate the real writing teaching and learning scene, each in- struction is within 15 minutes. For the control group, the students will receive a holistic score, marks for grammatical and lexical errors, and a general comment just like the daily teaching. With regard to the evaluation in the control group, the raters are also asked to finish the review of each composition within 10 minutes. In this way, the marks of errors and the comment may be rough, inadequate, or even faulty. Note that only the holistic score by the teacher will be presented to the writer. Even though MsCAEWL will review each essay and output the sub and overall scores as experimental data which are completely blind to the control group.
As for the experimental group, besides the ordinary writing instructions, their essays are completely evaluated by the system rather than the teacher. After finishing the writing and evaluation, the students may revise each composition according to MsCAEWL’s multiple trait-specific scores and writing feedback. The revision effect can be checked because the revised essay can be scored again immediately. This computer-assisted English writing learning round is without limitation of times. However, in this experiment, the revision shall not stop unless at least three rounds are completed, or the score stops rising. All writing tests are carried out according to the requirements of The National Matriculation English Test, with a full score scope of 100 credits.
