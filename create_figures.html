<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Research Figures - Dual-Model AWE System</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .figure-container {
            background: white;
            margin: 30px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            page-break-after: always;
        }
        .figure-title {
            text-align: center;
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 20px;
            color: #333;
        }
        .diagram {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            margin: 20px 0;
        }
        .box {
            fill: #e8f4fd;
            stroke: #2c5aa0;
            stroke-width: 2;
            rx: 8;
        }
        .process-box {
            fill: #fff2cc;
            stroke: #d6b656;
            stroke-width: 2;
            rx: 8;
        }
        .output-box {
            fill: #d5e8d4;
            stroke: #82b366;
            stroke-width: 2;
            rx: 8;
        }
        .neural-box {
            fill: #f8cecc;
            stroke: #b85450;
            stroke-width: 2;
            rx: 8;
        }
        .text {
            font-family: 'Times New Roman', serif;
            font-size: 12px;
            text-anchor: middle;
            dominant-baseline: middle;
            fill: #333;
        }
        .small-text {
            font-size: 10px;
        }
        .arrow {
            stroke: #333;
            stroke-width: 2;
            fill: none;
            marker-end: url(#arrowhead);
        }
        .flow-arrow {
            stroke: #666;
            stroke-width: 1.5;
            fill: none;
            marker-end: url(#arrowhead);
        }
        .chart-line {
            fill: none;
            stroke-width: 2;
        }
        .training-line {
            stroke: #2c5aa0;
        }
        .validation-line {
            stroke: #d6b656;
        }
        .grid-line {
            stroke: #ddd;
            stroke-width: 0.5;
        }
        .axis {
            stroke: #333;
            stroke-width: 1;
        }
        .axis-text {
            font-size: 10px;
            text-anchor: middle;
            fill: #333;
        }
    </style>
</head>
<body>

<!-- Figure 1: Dual-Model System Architecture -->
<div class="figure-container">
    <div class="figure-title">Figure 1. Dual-Model System Architecture Schema</div>
    <svg class="diagram" viewBox="0 0 800 600">
        <defs>
            <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                    refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
            </marker>
        </defs>
        
        <!-- Input -->
        <rect class="box" x="350" y="50" width="100" height="40"/>
        <text class="text" x="400" y="70">Input Text</text>
        
        <!-- Preprocessing -->
        <rect class="process-box" x="325" y="120" width="150" height="40"/>
        <text class="text" x="400" y="140">Text Preprocessing</text>
        
        <!-- Parallel Processing Paths -->
        <!-- Fluency Module -->
        <rect class="neural-box" x="150" y="200" width="120" height="60"/>
        <text class="text" x="210" y="220">BERT-based</text>
        <text class="text" x="210" y="240">Fluency Module</text>
        
        <!-- Correctness Module -->
        <rect class="neural-box" x="530" y="200" width="120" height="60"/>
        <text class="text" x="590" y="220">T5-based</text>
        <text class="text" x="590" y="240">Correctness Module</text>
        
        <!-- Fluency Score -->
        <rect class="output-box" x="150" y="300" width="120" height="40"/>
        <text class="text" x="210" y="320">Fluency Score (Sf)</text>
        
        <!-- Correctness Score -->
        <rect class="output-box" x="530" y="300" width="120" height="40"/>
        <text class="text" x="590" y="320">Correctness Score (Sc)</text>
        
        <!-- Score Integration -->
        <rect class="process-box" x="325" y="380" width="150" height="60"/>
        <text class="text" x="400" y="400">Score Integration</text>
        <text class="text small-text" x="400" y="420">w₁ × Sf + w₂ × Sc</text>
        
        <!-- Final Output -->
        <rect class="output-box" x="325" y="480" width="150" height="40"/>
        <text class="text" x="400" y="500">Final Assessment Score</text>
        
        <!-- Arrows -->
        <line class="arrow" x1="400" y1="90" x2="400" y2="120"/>
        <line class="arrow" x1="350" y1="160" x2="210" y2="200"/>
        <line class="arrow" x1="450" y1="160" x2="590" y2="200"/>
        <line class="arrow" x1="210" y1="260" x2="210" y2="300"/>
        <line class="arrow" x1="590" y1="260" x2="590" y2="300"/>
        <line class="arrow" x1="270" y1="320" x2="350" y2="380"/>
        <line class="arrow" x1="530" y1="320" x2="450" y2="380"/>
        <line class="arrow" x1="400" y1="440" x2="400" y2="480"/>
        
        <!-- Labels -->
        <text class="text small-text" x="280" y="180">Parallel Processing</text>
    </svg>
</div>

<!-- Figure 2: BERT Architecture -->
<div class="figure-container">
    <div class="figure-title">Figure 2. BERT-based Fluency Assessment Model Architecture</div>
    <svg class="diagram" viewBox="0 0 800 600">
        <!-- Input Text -->
        <rect class="box" x="350" y="50" width="100" height="30"/>
        <text class="text" x="400" y="65">Input Text</text>
        
        <!-- Tokenization -->
        <rect class="process-box" x="300" y="100" width="200" height="30"/>
        <text class="text" x="400" y="115">WordPiece Tokenization</text>
        
        <!-- Token Embeddings -->
        <rect class="neural-box" x="50" y="160" width="120" height="40"/>
        <text class="text" x="110" y="175">Token</text>
        <text class="text" x="110" y="190">Embeddings</text>
        
        <rect class="neural-box" x="200" y="160" width="120" height="40"/>
        <text class="text" x="260" y="175">Position</text>
        <text class="text" x="260" y="190">Embeddings</text>
        
        <rect class="neural-box" x="350" y="160" width="120" height="40"/>
        <text class="text" x="410" y="175">Segment</text>
        <text class="text" x="410" y="190">Embeddings</text>
        
        <!-- Transformer Layers -->
        <rect class="neural-box" x="250" y="240" width="300" height="40"/>
        <text class="text" x="400" y="260">12 Transformer Layers (Multi-Head Attention)</text>
        
        <!-- CLS Token -->
        <rect class="output-box" x="325" y="320" width="150" height="40"/>
        <text class="text" x="400" y="340">[CLS] Token Representation</text>
        
        <!-- Regression Head -->
        <rect class="process-box" x="300" y="400" width="200" height="30"/>
        <text class="text" x="400" y="415">Regression Head (FC + Dropout)</text>
        
        <!-- Sigmoid -->
        <rect class="process-box" x="325" y="460" width="150" height="30"/>
        <text class="text" x="400" y="475">Sigmoid Activation</text>
        
        <!-- Output -->
        <rect class="output-box" x="325" y="520" width="150" height="30"/>
        <text class="text" x="400" y="535">Fluency Score [0,1]</text>
        
        <!-- Arrows -->
        <line class="arrow" x1="400" y1="80" x2="400" y2="100"/>
        <line class="arrow" x1="350" y1="130" x2="110" y2="160"/>
        <line class="arrow" x1="400" y1="130" x2="260" y2="160"/>
        <line class="arrow" x1="450" y1="130" x2="410" y2="160"/>
        <line class="arrow" x1="200" y1="200" x2="300" y2="240"/>
        <line class="arrow" x1="400" y1="200" x2="400" y2="240"/>
        <line class="arrow" x1="500" y1="200" x2="500" y2="240"/>
        <line class="arrow" x1="400" y1="280" x2="400" y2="320"/>
        <line class="arrow" x1="400" y1="360" x2="400" y2="400"/>
        <line class="arrow" x1="400" y1="430" x2="400" y2="460"/>
        <line class="arrow" x1="400" y1="490" x2="400" y2="520"/>
    </svg>
</div>

<!-- Figure 3: T5 Workflow -->
<div class="figure-container">
    <div class="figure-title">Figure 3. T5-based Correctness Assessment Workflow</div>
    <svg class="diagram" viewBox="0 0 800 600">
        <!-- Input -->
        <rect class="box" x="350" y="50" width="100" height="30"/>
        <text class="text" x="400" y="65">Original Text</text>
        
        <!-- Preprocessing -->
        <rect class="process-box" x="300" y="100" width="200" height="30"/>
        <text class="text" x="400" y="115">Text Normalization & Tokenization</text>
        
        <!-- T5 Encoder -->
        <rect class="neural-box" x="150" y="170" width="150" height="60"/>
        <text class="text" x="225" y="190">T5 Encoder</text>
        <text class="text small-text" x="225" y="210">Contextualized</text>
        <text class="text small-text" x="225" y="225">Representations</text>
        
        <!-- T5 Decoder -->
        <rect class="neural-box" x="500" y="170" width="150" height="60"/>
        <text class="text" x="575" y="190">T5 Decoder</text>
        <text class="text small-text" x="575" y="210">Error Correction</text>
        <text class="text small-text" x="575" y="225">Generation</text>
        
        <!-- Corrected Text -->
        <rect class="output-box" x="500" y="270" width="150" height="40"/>
        <text class="text" x="575" y="290">Corrected Text</text>
        
        <!-- Error Analysis -->
        <rect class="process-box" x="300" y="350" width="200" height="60"/>
        <text class="text" x="400" y="370">Error Quantification</text>
        <text class="text small-text" x="400" y="385">Token-level Comparison</text>
        <text class="text small-text" x="400" y="400">Error Categorization</text>
        
        <!-- Error Ratio -->
        <rect class="process-box" x="150" y="450" width="150" height="40"/>
        <text class="text" x="225" y="470">Error Ratio</text>
        <text class="text small-text" x="225" y="485">Corrections/Tokens</text>
        
        <!-- Scoring Function -->
        <rect class="process-box" x="500" y="450" width="150" height="40"/>
        <text class="text" x="575" y="470">Exponential Decay</text>
        <text class="text small-text" x="575" y="485">100 × e^(-λ × ratio)</text>
        
        <!-- Final Score -->
        <rect class="output-box" x="325" y="530" width="150" height="30"/>
        <text class="text" x="400" y="545">Correctness Score</text>
        
        <!-- Arrows -->
        <line class="arrow" x1="400" y1="80" x2="400" y2="100"/>
        <line class="arrow" x1="350" y1="130" x2="225" y2="170"/>
        <line class="arrow" x1="300" y1="200" x2="500" y2="200"/>
        <line class="arrow" x1="575" y1="230" x2="575" y2="270"/>
        <line class="arrow" x1="450" y1="130" x2="575" y2="170"/>
        <line class="arrow" x1="500" y1="290" x2="450" y2="350"/>
        <line class="arrow" x1="350" y1="130" x2="350" y2="350"/>
        <line class="arrow" x1="350" y1="410" x2="225" y2="450"/>
        <line class="arrow" x1="450" y1="410" x2="575" y2="450"/>
        <line class="arrow" x1="300" y1="470" x2="350" y2="530"/>
        <line class="arrow" x1="500" y1="470" x2="450" y2="530"/>
    </svg>
</div>

<!-- Figure 4: Training Progress -->
<div class="figure-container">
    <div class="figure-title">Figure 4. Training Progress Analysis for Fluency Assessment Model</div>
    <svg class="diagram" viewBox="0 0 800 600">
        <!-- Chart Background -->
        <rect x="100" y="80" width="600" height="400" fill="white" stroke="#ddd"/>
        
        <!-- Grid Lines -->
        <line class="grid-line" x1="100" y1="130" x2="700" y2="130"/>
        <line class="grid-line" x1="100" y1="180" x2="700" y2="180"/>
        <line class="grid-line" x1="100" y1="230" x2="700" y2="230"/>
        <line class="grid-line" x1="100" y1="280" x2="700" y2="280"/>
        <line class="grid-line" x1="100" y1="330" x2="700" y2="330"/>
        <line class="grid-line" x1="100" y1="380" x2="700" y2="380"/>
        <line class="grid-line" x1="100" y1="430" x2="700" y2="430"/>
        
        <line class="grid-line" x1="200" y1="80" x2="200" y2="480"/>
        <line class="grid-line" x1="300" y1="80" x2="300" y2="480"/>
        <line class="grid-line" x1="400" y1="80" x2="400" y2="480"/>
        <line class="grid-line" x1="500" y1="80" x2="500" y2="480"/>
        <line class="grid-line" x1="600" y1="80" x2="600" y2="480"/>
        
        <!-- Axes -->
        <line class="axis" x1="100" y1="480" x2="700" y2="480"/>
        <line class="axis" x1="100" y1="80" x2="100" y2="480"/>
        
        <!-- Training Loss Curve -->
        <polyline class="chart-line training-line" 
                  points="100,450 200,380 300,320 400,280 500,250 600,230 700,220"/>
        
        <!-- Validation Loss Curve -->
        <polyline class="chart-line validation-line" 
                  points="100,460 200,400 300,350 400,320 500,300 600,290 700,285"/>
        
        <!-- Axis Labels -->
        <text class="axis-text" x="400" y="510">Training Epochs</text>
        <text class="axis-text" x="50" y="280" transform="rotate(-90 50 280)">Loss (MSE)</text>
        
        <!-- Epoch Numbers -->
        <text class="axis-text" x="100" y="500">0</text>
        <text class="axis-text" x="200" y="500">0.5</text>
        <text class="axis-text" x="300" y="500">1.0</text>
        <text class="axis-text" x="400" y="500">1.5</text>
        <text class="axis-text" x="500" y="500">2.0</text>
        <text class="axis-text" x="600" y="500">2.5</text>
        <text class="axis-text" x="700" y="500">3.0</text>
        
        <!-- Loss Values -->
        <text class="axis-text" x="85" y="485">0.0</text>
        <text class="axis-text" x="85" y="435">0.1</text>
        <text class="axis-text" x="85" y="385">0.2</text>
        <text class="axis-text" x="85" y="335">0.3</text>
        <text class="axis-text" x="85" y="285">0.4</text>
        <text class="axis-text" x="85" y="235">0.5</text>
        <text class="axis-text" x="85" y="185">0.6</text>
        <text class="axis-text" x="85" y="135">0.7</text>
        <text class="axis-text" x="85" y="85">0.8</text>
        
        <!-- Legend -->
        <rect x="520" y="120" width="150" height="60" fill="white" stroke="#333"/>
        <line class="chart-line training-line" x1="530" y1="135" x2="560" y2="135"/>
        <text class="text small-text" x="570" y="140">Training Loss</text>
        <line class="chart-line validation-line" x1="530" y1="155" x2="560" y2="155"/>
        <text class="text small-text" x="570" y="160">Validation Loss</text>
        
        <!-- Performance Metrics Box -->
        <rect x="520" y="200" width="150" height="80" fill="#f9f9f9" stroke="#333"/>
        <text class="text small-text" x="595" y="220">Final Metrics:</text>
        <text class="text small-text" x="595" y="240">Correlation: 0.944</text>
        <text class="text small-text" x="595" y="255">MAE: 0.142</text>
        <text class="text small-text" x="595" y="270">RMSE: 0.218</text>
    </svg>
</div>

</body>
</html>
