<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Figure 2</title>
<style>
body { font-family: 'Times New Roman', serif; margin: 20px; background: white; }
.figure-title { text-align: center; font-weight: bold; font-size: 14px; margin-bottom: 20px; }
.diagram { width: 100%; height: 600px; border: 1px solid #ddd; }
.box { fill: #e8f4fd; stroke: #2c5aa0; stroke-width: 2; rx: 8; }
.process-box { fill: #fff2cc; stroke: #d6b656; stroke-width: 2; rx: 8; }
.output-box { fill: #d5e8d4; stroke: #82b366; stroke-width: 2; rx: 8; }
.neural-box { fill: #f8cecc; stroke: #b85450; stroke-width: 2; rx: 8; }
.text { font-family: 'Times New Roman', serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; fill: #333; }
.arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
</style></head><body>
<div class="figure-title">Figure 2. BERT-based Fluency Assessment Model Architecture</div>
<svg class="diagram" viewBox="0 0 800 600">
<defs><marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
<polygon points="0 0, 10 3.5, 0 7" fill="#333" /></marker></defs>
<rect class="box" x="350" y="50" width="100" height="30"/>
<text class="text" x="400" y="65">Input Text</text>
<rect class="process-box" x="300" y="100" width="200" height="30"/>
<text class="text" x="400" y="115">WordPiece Tokenization</text>
<rect class="neural-box" x="50" y="160" width="120" height="40"/>
<text class="text" x="110" y="175">Token</text>
<text class="text" x="110" y="190">Embeddings</text>
<rect class="neural-box" x="200" y="160" width="120" height="40"/>
<text class="text" x="260" y="175">Position</text>
<text class="text" x="260" y="190">Embeddings</text>
<rect class="neural-box" x="350" y="160" width="120" height="40"/>
<text class="text" x="410" y="175">Segment</text>
<text class="text" x="410" y="190">Embeddings</text>
<rect class="neural-box" x="250" y="240" width="300" height="40"/>
<text class="text" x="400" y="260">12 Transformer Layers (Multi-Head Attention)</text>
<rect class="output-box" x="325" y="320" width="150" height="40"/>
<text class="text" x="400" y="340">[CLS] Token Representation</text>
<rect class="process-box" x="300" y="400" width="200" height="30"/>
<text class="text" x="400" y="415">Regression Head (FC + Dropout)</text>
<rect class="process-box" x="325" y="460" width="150" height="30"/>
<text class="text" x="400" y="475">Sigmoid Activation</text>
<rect class="output-box" x="325" y="520" width="150" height="30"/>
<text class="text" x="400" y="535">Fluency Score [0,1]</text>
<line class="arrow" x1="400" y1="80" x2="400" y2="100"/>
<line class="arrow" x1="350" y1="130" x2="110" y2="160"/>
<line class="arrow" x1="400" y1="130" x2="260" y2="160"/>
<line class="arrow" x1="450" y1="130" x2="410" y2="160"/>
<line class="arrow" x1="200" y1="200" x2="300" y2="240"/>
<line class="arrow" x1="400" y1="200" x2="400" y2="240"/>
<line class="arrow" x1="500" y1="200" x2="500" y2="240"/>
<line class="arrow" x1="400" y1="280" x2="400" y2="320"/>
<line class="arrow" x1="400" y1="360" x2="400" y2="400"/>
<line class="arrow" x1="400" y1="430" x2="400" y2="460"/>
<line class="arrow" x1="400" y1="490" x2="400" y2="520"/>
</svg></body></html>