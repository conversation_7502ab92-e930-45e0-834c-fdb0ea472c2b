<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>Figure 1</title>
<style>
body { font-family: 'Times New Roman', serif; margin: 20px; background: white; }
.figure-title { text-align: center; font-weight: bold; font-size: 14px; margin-bottom: 20px; }
.diagram { width: 100%; height: 600px; border: 1px solid #ddd; }
.box { fill: #e8f4fd; stroke: #2c5aa0; stroke-width: 2; rx: 8; }
.process-box { fill: #fff2cc; stroke: #d6b656; stroke-width: 2; rx: 8; }
.output-box { fill: #d5e8d4; stroke: #82b366; stroke-width: 2; rx: 8; }
.neural-box { fill: #f8cecc; stroke: #b85450; stroke-width: 2; rx: 8; }
.text { font-family: 'Times New Roman', serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; fill: #333; }
.small-text { font-size: 10px; }
.arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
</style></head><body>
<div class="figure-title">Figure 1. Dual-Model System Architecture Schema</div>
<svg class="diagram" viewBox="0 0 800 600">
<defs><marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
<polygon points="0 0, 10 3.5, 0 7" fill="#333" /></marker></defs>
<rect class="box" x="350" y="50" width="100" height="40"/>
<text class="text" x="400" y="70">Input Text</text>
<rect class="process-box" x="325" y="120" width="150" height="40"/>
<text class="text" x="400" y="140">Text Preprocessing</text>
<rect class="neural-box" x="150" y="200" width="120" height="60"/>
<text class="text" x="210" y="220">BERT-based</text>
<text class="text" x="210" y="240">Fluency Module</text>
<rect class="neural-box" x="530" y="200" width="120" height="60"/>
<text class="text" x="590" y="220">T5-based</text>
<text class="text" x="590" y="240">Correctness Module</text>
<rect class="output-box" x="150" y="300" width="120" height="40"/>
<text class="text" x="210" y="320">Fluency Score (Sf)</text>
<rect class="output-box" x="530" y="300" width="120" height="40"/>
<text class="text" x="590" y="320">Correctness Score (Sc)</text>
<rect class="process-box" x="325" y="380" width="150" height="60"/>
<text class="text" x="400" y="400">Score Integration</text>
<text class="text small-text" x="400" y="420">w₁ × Sf + w₂ × Sc</text>
<rect class="output-box" x="325" y="480" width="150" height="40"/>
<text class="text" x="400" y="500">Final Assessment Score</text>
<line class="arrow" x1="400" y1="90" x2="400" y2="120"/>
<line class="arrow" x1="350" y1="160" x2="210" y2="200"/>
<line class="arrow" x1="450" y1="160" x2="590" y2="200"/>
<line class="arrow" x1="210" y1="260" x2="210" y2="300"/>
<line class="arrow" x1="590" y1="260" x2="590" y2="300"/>
<line class="arrow" x1="270" y1="320" x2="350" y2="380"/>
<line class="arrow" x1="530" y1="320" x2="450" y2="380"/>
<line class="arrow" x1="400" y1="440" x2="400" y2="480"/>
<text class="text small-text" x="280" y="180">Parallel Processing</text>
</svg></body></html>