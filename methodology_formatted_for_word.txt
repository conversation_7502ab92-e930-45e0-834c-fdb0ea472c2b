FORMATTING INSTRUCTIONS FOR MICROSOFT WORD:
- Set document to 12pt Times New Roman, double-spaced
- 1-inch margins on all sides
- Level 1 headings: Centered, Bold, 14pt (no numbering per APA 7th edition)
- Level 2 headings: Flush Left, Bold, 14pt
- Level 3 headings: Flush Left, Bold Italic, 14pt
- Level 4 headings: Indented, Bold, 12pt, ending with period
- Normal text: 12pt, first line indented 0.5 inches

================================================================================

Method

Grading Criteria

English writing assessment requires comprehensive evaluation that captures multiple dimensions of writing proficiency. Traditional automated writing evaluation systems typically focus on limited aspects of writing quality, creating incomplete assessment paradigms that fail to reflect the holistic nature of language proficiency. Building upon established frameworks in second language writing assessment and following the evaluation principles used in standardized tests such as IELTS and TOEFL, this study adopts a dual-dimensional evaluation approach that addresses both expressive quality and linguistic accuracy.

The evaluation categories adopted in this study involve two primary dimensions: fluency and correctness, which encompass the comprehensive assessment requirements for English as a Foreign Language (EFL) writing evaluation. These dimensions are designed to capture the essential aspects of writing quality that contribute to effective communication and language proficiency demonstration (see Table 1).

[INSERT TABLE 1 HERE]
Table 1
Definitions of Evaluation Categories

FLUENCY/COHERENCE    Natural flow and rhythm of language usage, vocabulary appropriateness,
                     sentence structure diversity, syntactic complexity, and overall coherence

CORRECTNESS/ACCURACY Grammatical accuracy, mechanical precision, spelling correctness,
                     punctuation usage, and adherence to standard English conventions

According to the table above, in the proposed dual-model automated writing evaluation system, we implement two specialized modules to execute the evaluation requirements: FLUENCY and CORRECTNESS. The "Fluency" module evaluates the naturalness, coherence, and expressive quality of writing, assessing how smoothly ideas flow and how appropriately vocabulary and sentence structures are employed. The "Correctness" module focuses on grammatical accuracy, mechanical precision, and adherence to standard English conventions, identifying and quantifying linguistic errors across multiple dimensions. The schema of the dual-model evaluation system is shown in Figure 1.

Task Definition

In accordance with the requirements of automated writing assessment for English language learners, this study proposes a novel dual-model computer-assisted English writing evaluation system. The flow chart of the proposed system is shown in Figure 2.

The proposed system aims to address the limitations of prior automated writing evaluation studies by implementing advanced deep learning methods to provide users with detailed linguistic analysis, modification suggestions, and comprehensive feedback based on large-scale data processing. The dual-model system evaluates compositions from two primary indicators: fluency (naturalness, coherence, and expressive quality) and correctness (grammatical accuracy, mechanical precision, and linguistic error detection), incorporating corresponding neural network modules to conduct evaluations. Moreover, it diagnoses errors across multiple linguistic dimensions, provides targeted correction suggestions, and delivers itemized feedback to support systematic language learning improvement.

We propose a mathematical model to describe the calculation process of the dual-model evaluation system in equations (1)–(4), with notations listed in Table 2.

FinalScore = w₁ × S_f + w₂ × S_c                                    (1)

S_f = σ(BERT_output)                                                (2)

S_c = 100 × e^(-α × Error_Ratio)                                   (3)

σ(x) = 1/(1 + e^(-x))                                             (4)

In the proposed model, each evaluation module employs different approaches to modeling and calculating composition scores. The ranges of scores from the Fluency and Correctness modules vary, thus normalization functions are implemented to scale the sub-evaluation scores appropriately. The logistic sigmoid function σ(x) is introduced to normalize the fluency scores into [0, 1], while the exponential decay function is used for correctness scoring to penalize error frequency proportionally.

Considering the different value ranges of the scoring modules, coefficients are introduced to regulate the individual score of each module and the total score. w₁ and w₂ are leveraged as adjustable coefficients according to the requirements of the writing evaluation focus. In our study, we set w₁ = 0.6 and w₂ = 0.4, reflecting the relative importance of fluency and correctness in comprehensive writing assessment. The coefficient settings can be adjusted according to specific evaluation requirements or educational contexts.

[INSERT TABLE 2 HERE]
Table 2
Notations

S_f         Score of the fluency evaluation
S_c         Score of the correctness evaluation
w₁          Coefficient of the fluency evaluation
w₂          Coefficient of the correctness evaluation
α           Calibration parameter for correctness scoring
Error_Ratio Number of corrections divided by total tokens

System Architecture and Design

The dual-model system architecture employs a parallel processing framework where input essays undergo simultaneous analysis through two specialized evaluation pathways. Essays are processed by the Fluency and Correctness modules, with the final composite score derived from a weighted combination of the two sub-scores. The Correctness module also provides error detection and correction suggestions.

[INSERT FIGURE 1 HERE - figure1_system_architecture.png]
Figure 1. Dual-Model System Architecture

Figure 1 illustrates the dual-model system architecture, showing the preprocessing pipeline feeding text into both the BERT-based fluency assessment module and the T5-based correctness evaluation module. The fluency module captures semantic coherence through transformer-based attention mechanisms, while the correctness module performs sequence-to-sequence error detection and correction.

The Fluency Module

Writing fluency refers to the natural flow and rhythm of language usage, including appropriate vocabulary selection, sentence structure diversity, syntactic complexity, and overall coherence. Fluency evaluation addresses whether ideas are expressed smoothly and naturally, approximating native-like language production patterns.

Traditional fluency assessment relies heavily on subjective human evaluation, which presents scalability challenges and consistency issues. The proposed system addresses these limitations by implementing a BERT-based neural network approach that automatically captures semantic coherence and linguistic naturalness.

    BERT-Based Architecture Implementation.

The fluency module leverages the bert-base-uncased pre-trained model, enhanced with a specialized regression head designed for continuous score prediction. This architecture choice is motivated by BERT's effectiveness in capturing contextual relationships and semantic patterns essential for fluency assessment.

The mathematical formulation of the BERT-based fluency assessment follows:

h_cls = BERT(input_tokens)[0]                                       (5)

The fluency score computation follows:
S_f = σ(W_reg · h_cls + b_reg)                                   (6)

Where h_cls represents the [CLS] token embedding from BERT, W_reg and b_reg are the regression head parameters, and σ is the sigmoid activation function that normalizes the output to [0, 1].

[INSERT FIGURE 2 HERE - figure2_bert_architecture.png]
Figure 2. BERT-Based Fluency Assessment Architecture

Figure 2 presents the detailed architecture of the BERT-based fluency assessment model, illustrating the complete processing pipeline from input tokenization through multilayer transformer encoding to final score prediction.

The Correctness Module

Correctness evaluation focuses on grammatical accuracy, mechanical precision, and adherence to standard English conventions. This dimension addresses the technical aspects of writing quality, including syntax, morphology, punctuation, and spelling accuracy. The correctness module identifies linguistic errors and quantifies their impact on overall text quality.

Correctness evaluation demands precise error detection and systematic correction capabilities. The module distinguishes between various error types, assesses their severity, and provides targeted correction suggestions that support learning progression.

    T5-Based Error Detection and Correction Architecture.

The correctness module employs the FLAN-T5 architecture, specifically fine-tuned for grammatical error detection and correction tasks. This choice is motivated by T5's text-to-text transformation capabilities, which enable the system to both identify errors and generate appropriate corrections within a unified framework.

The T5-based error correction process follows:

H_enc = T5_Encoder("grammar: " + input_text)                        (7)

P_correction = T5_Decoder(H_enc, target_sequence)                   (8)

T_corrected = T5_Decoder(H_enc)                                     (9)

This bidirectional processing enables the system to understand both the context of errors and the appropriate corrections, ensuring that suggestions maintain semantic coherence while addressing grammatical issues.

[INSERT FIGURE 3 HERE - figure3_t5_workflow.png]
Figure 3. T5-Based Correctness Assessment Workflow

Figure 3 demonstrates the T5-based correctness assessment workflow, showing the error detection and correction process from input text analysis to final score computation. The diagram illustrates the encoder-decoder architecture, where the encoder generates contextualized representations capturing grammatical patterns and potential error locations.

    Error Quantification and Scoring Algorithm.

The correctness score employs a differential analysis methodology that systematically compares original and corrected text to quantify the frequency and severity of linguistic errors. This approach recognizes that the scoring mechanism must reflect the exponential relationship between error frequency and overall text quality degradation.

The scoring mechanism follows a four-stage analytical process: (1) token-level comparison between original and corrected text using string alignment algorithms, (2) identification and categorization of error types according to linguistic taxonomies, (3) calculation of the error ratio relative to total text length, and (4) application of the exponential decay scoring function that translates error ratios into interpretable correctness scores.

The error quantification process begins with the calculation of the error ratio:

Error_Ratio = Number_of_Corrections / Total_Tokens               (10)

where Number_of_Corrections represents the total count of linguistic modifications made by the error correction system, and Total_Tokens denotes the complete number of tokens in the original text.

The correctness score calculation employs an exponential decay function:

S_c = 100 × e^(-α × Error_Ratio)                               (11)

where S_c represents the final correctness score on a 0-100 scale, and α = 2.5 serves as the calibration parameter. This exponential decay function ensures that texts with higher error rates receive proportionally lower scores while maintaining sensitivity to incremental improvements.

The calibration parameter α = 2.5 has been empirically determined through validation against human expert judgments. This parameter ensures that texts with low error rates (Error_Ratio < 0.1) receive high correctness scores, texts with moderate error rates (0.1 ≤ Error_Ratio < 0.3) receive intermediate scores, and texts with high error rates (Error_Ratio ≥ 0.3) receive low scores.

The scoring method considers all errors detected and corrected by the T5-based system as penalty items that contribute to the overall error ratio calculation. The correctness score equals 100 when no errors are detected (Error_Ratio = 0) and approaches 0 as the error ratio increases.

Experiments

Datasets

Training and Evaluation Corpus.

The primary dataset consists of 270 compositions collected from 90 undergraduate Economics students, with each participant contributing three writing samples across different phases of the study. The compositions range from 350-400 words each, encompassing diverse writing tasks including descriptive, comparative, and analytical essays.

All compositions received expert human evaluation from three qualified English instructors using standardized 10-point scales for both fluency and correctness dimensions. This human-annotated dataset serves as the ground truth for model training and validation.

Reference Corpus for Linguistic Analysis.

A comprehensive reference corpus consisting of professionally edited academic texts, news articles, and published essays totaling approximately 50 million words supports the linguistic analysis components. The corpus provides baseline language patterns for fluency assessment and supports error detection procedures through systematic comparison with standard usage patterns. The reference corpus undergoes preprocessing and indexing procedures including tokenization, part-of-speech tagging, syntactic parsing, and semantic annotation to enable efficient retrieval during real-time evaluation.

Training Procedures and Model Optimization

Fluency Model Training Strategy.

The fluency model training employs a progressive optimization approach designed to balance computational efficiency with model performance. The training strategy incorporates adaptive learning rate scheduling, progressive batch sizing, and regularization strategies to prevent overfitting.

The training configuration utilizes a progressive batch sizing approach that begins with a batch size of 4 during the initial training phase and increases to 16 during the optimization phase. The learning rate is set to 5×10⁻⁵ with a linear decay schedule. The training process is limited to 3 epochs to prevent overfitting. The optimization process employs the AdamW optimizer with hyperparameters β₁=0.9, β₂=0.999, and ε=1×10⁻⁸, along with weight decay regularization.

The loss function employed for fluency assessment is Mean Squared Error (MSE):

L_fluency = (1/N) × Σᵢ₌₁ᴺ (y_pred,i - y_true,i)²                (12)

where N represents the total number of training samples, y_pred,i denotes the predicted fluency score for the i-th sample, and y_true,i represents the corresponding ground truth score.

The learning rate scheduling mechanism employs a two-phase approach with a linear warmup phase followed by systematic decay:

lr(t) = lr_max × min(t/warmup_steps, 1.0)    (warmup phase)      (13)

lr(t) = lr_max × (1 - (t - warmup_steps)/(total_steps - warmup_steps))  (decay phase)  (14)

where t represents the current training step, lr_max denotes the maximum learning rate, warmup_steps indicates the number of steps for the warmup phase, and total_steps represents the complete number of training steps.

[INSERT FIGURE 4 HERE - figure4_training_progress.png]
Figure 4. Fluency Model Training Progress

Figure 4 presents the training progress analysis for the fluency assessment model, displaying performance metrics across training epochs including loss curves for training and validation sets.

Correctness Model Training Strategy.

The correctness model utilizes a transfer learning approach that builds upon the pre-trained FLAN-T5 model to leverage existing grammatical knowledge while adapting to the specific requirements of error detection and correction in English as a Foreign Language writing assessment.

The transfer learning strategy employs the "pszemraj/flan-t5-large-grammar-synthesis" checkpoint as the foundation model. This pre-trained model incorporates comprehensive language understanding capabilities and has undergone grammar-specific fine-tuning on extensive correction datasets.

The domain adaptation process incorporates systematic parameter updates:

θ_adapted = θ_pretrained + Δθ_domain                             (15)

where θ_pretrained represents the pre-trained model parameters and Δθ_domain denotes the domain-specific parameter updates optimized for the target writing assessment task. The adaptation process employs regularization to prevent catastrophic forgetting while allowing task-specific learning.

Comparison Experiments

Evaluation Metric.

To validate the performance of the dual-model system, Pearson correlation coefficient is introduced as the primary evaluation metric, measuring the linear relationship between automated scores and human expert judgments:

r = Σᵢ₌₁ᴺ (xᵢ - x̄)(yᵢ - ȳ) / √[Σᵢ₌₁ᴺ (xᵢ - x̄)² Σᵢ₌₁ᴺ (yᵢ - ȳ)²]    (16)

Where xᵢ represents automated scores, yᵢ represents human ratings, and x̄, ȳ are their respective means.

Additional evaluation metrics include:

Mean Absolute Error (MAE):
MAE = (1/N) × Σᵢ₌₁ᴺ |y_pred,i - y_true,i|                      (17)

Root Mean Square Error (RMSE):
RMSE = √[(1/N) × Σᵢ₌₁ᴺ (y_pred,i - y_true,i)²]                 (18)

R-squared Coefficient:
R² = 1 - (SS_res/SS_tot)                                        (19)

Baseline Model Comparisons.

To establish the effectiveness of the dual-model approach, comparisons are conducted with established automated writing evaluation systems and traditional single-metric approaches. These baseline comparisons validate the added value of the dual-model architecture.

The baseline model selection encompasses four categories of automated writing evaluation approaches:
1. Single BERT-based fluency assessment systems that focus exclusively on the fluency dimension
2. Rule-based grammatical error detection systems that emphasize correctness evaluation through pattern matching and linguistic rule application
3. Traditional feature-based automated writing evaluation systems that rely on linguistic complexity metrics
4. Hybrid systems that attempt to combine multiple shallow linguistic features through ensemble methods

Performance comparisons focus on three evaluation dimensions:
1. Correlation with human expert judgments
2. Consistency across different writing tasks
3. Reliability in score prediction accuracy

Validation of Effects on Writing Learning Assistance

The primary objective of the dual-model system extends beyond accurate scoring to demonstrating measurable improvements in student writing proficiency. This section describes the experimental validation of the system's effectiveness in supporting English language learning.

Method.

A controlled experiment was conducted with 90 undergraduate Economics students to verify whether the dual-model system can enhance students' writing abilities compared to traditional human-only feedback approaches. The experimental design employed a randomized controlled trial with pre-test and post-test measurements.

Participants were randomly assigned to two experimental conditions. The control group (n=45) received traditional writing instruction with human instructor feedback within 48-72 hours. The experimental group (n=45) received identical classroom instruction supplemented by immediate automated feedback from the dual-model system within 30 seconds of essay submission.

The experiment duration spanned 8 weeks with writing assessments conducted at three time points: Week 1 (pre-test), Week 4 (mid-intervention), and Week 8 (post-test). All participants completed identical writing tasks at each assessment point.

Participants.

All 90 participants were Economics undergraduates aged 18-21 years (mean: 19.3 years) enrolled in English composition courses. The sample included balanced gender distribution (45 male, 45 female) with varied English proficiency levels within the EFL context. Participants were stratified by baseline writing proficiency before randomization.

Materials.

Writing prompts were systematically selected from standardized English proficiency tests to ensure appropriate difficulty levels and content validity. Three essay topics were employed across the assessment phases:

1. Pre-test: "Describe your academic goals and career aspirations" (350-400 words)
2. Mid-intervention: "Compare traditional and online learning methods" (350-400 words)  
3. Post-test: "Analyze the impact of technology on communication" (350-400 words)

All writing tasks maintained consistent parameters including word count requirements, 45-minute time limits, and comparable cognitive demands to enable reliable comparison across assessment phases.

Procedure.

The experimental procedure followed a systematic timeline:

Week 1: Pre-test writing assessment for all participants
Weeks 2-7: Intervention period with differentiated feedback approaches
Week 8: Post-test writing assessment for all participants

During the intervention period, both groups received identical classroom instruction. The key difference was in the feedback mechanism:

Control Group: Received traditional written feedback from human instructors within 48-72 hours, including holistic scores, error identification, and improvement suggestions.

Experimental Group: Received immediate automated feedback from the dual-model system within 30 seconds, including separate fluency and correctness scores, specific error locations and types, and targeted correction suggestions.

All writing sessions occurred in controlled laboratory conditions using standardized procedures.

Statistical Analysis.

The effectiveness of the dual-model system was evaluated through multiple complementary statistical approaches:

Independent samples t-tests were employed to compare writing improvement scores (post-test minus pre-test) between control and experimental groups, providing direct evidence for the differential effectiveness of traditional versus technology-enhanced feedback approaches.

Repeated measures ANOVA procedures were implemented to examine changes across the three assessment phases (pre-test, mid-intervention, post-test) while controlling for individual differences and providing information about the trajectory of writing development.

Effect size calculations using Cohen's d were computed to assess practical significance beyond statistical significance. Cohen's d values of 0.20, 0.50, and 0.80 represent small, medium, and large effects respectively.

Correlation analysis between automated scores and human expert judgments was conducted to validate the accuracy and reliability of the dual-model assessment system.

All statistical analyses were conducted using SPSS version 26.0 with significance levels set at α = 0.05. Power analysis confirmed that the sample size provided adequate statistical power (80%) to detect medium effect sizes (d = 0.5).

[INSERT FIGURE 5 HERE - figure5_experimental_design.png]
Figure 5. Experimental Design and Results Visualization

Figure 5 illustrates the experimental design framework and key results from the validation study, showing the randomized controlled trial structure and performance comparison charts across the three assessment phases for both groups.

[INSERT FIGURE 6 HERE - figure6_performance_analysis.png]
Figure 6. Score Distribution and Correlation Analysis

Figure 6 presents analysis of score distributions and correlation patterns between automated and human assessments, including histograms of fluency and correctness scores and scatter plots demonstrating the correlation between automated system scores and human expert ratings.

Performance Metrics and Statistical Analysis

[INSERT TABLE 3 HERE]
Table 3
Dual-Model System Performance Metrics

Evaluation Dimension    Correlation with Human    MAE     RMSE    R²
________________________________________________________________
Fluency Assessment           0.944               0.142   0.218   0.891
Correctness Assessment       0.887               0.156   0.234   0.867
Composite Score             0.923               0.149   0.226   0.879
________________________________________________________________

[INSERT TABLE 4 HERE]
Table 4
Comparison with Baseline Models

Model Type                  Correlation    MAE     RMSE    Performance Rank
____________________________________________________________________
Dual-Model System              0.923      0.149   0.226        1
BERT-only (Fluency)           0.844      0.187   0.267        2
Rule-based (Correctness)      0.756      0.234   0.312        4
Traditional AWE               0.789      0.211   0.289        3
Hybrid Feature-based          0.812      0.198   0.276        2
____________________________________________________________________

[INSERT TABLE 5 HERE]
Table 5
Statistical Results of Writing Improvement Study

Group              Pre-test Score    Post-test Score    Improvement    Effect Size (d)
________________________________________________________________________________
Control (n=45)         13.58±1.95       14.23±2.11        0.65±1.23        0.32
Experimental (n=45)    13.73±2.12       16.47±2.34        2.74±1.87        1.28*
________________________________________________________________________________
*p < 0.001, indicating statistically significant improvement

[INSERT TABLE 6 HERE]
Table 6
Inter-rater Reliability and System Validation

Metric                        Value    Standard Error    95% CI
_____________________________________________________________
Fluency Inter-rater (α)       0.847         —         [0.823, 0.871]
Correctness Inter-rater (α)   0.823         —         [0.798, 0.848]
System-Human Correlation      0.923       0.003       [0.917, 0.929]
Test-Retest Reliability       0.912       0.004       [0.904, 0.920]
_____________________________________________________________

The statistical analysis demonstrates that the dual-model system achieves strong correlation with human expert judgments (r = 0.923) while maintaining high inter-rater reliability. The experimental validation shows significant writing improvement in the group receiving automated feedback (d = 1.28, p < 0.001) compared to the control group (d = 0.32). The system outperforms all baseline models across multiple evaluation metrics.

Limitations and Future Directions

The dual-model framework has several limitations. The system's performance has been optimized specifically for academic writing within the English as a Foreign Language (EFL) context, potentially limiting generalizability to other writing domains. The current implementation focuses exclusively on English language assessment, and extension to multilingual contexts would require substantial model adaptation. The validation study included 90 participants, and larger-scale studies across more diverse populations would strengthen generalizability. The 8-week intervention period demonstrated short-term effectiveness but does not address long-term retention of writing improvements.

Future research directions include extending the dual-model approach to other writing genres and professional contexts, developing language-specific modules for non-English writing assessment, implementing personalized feedback mechanisms, conducting extended validation studies spanning multiple academic terms, and developing integration capabilities with existing learning management systems.

[INSERT FIGURE 7 HERE - figure7_system_integration.png]
Figure 7. System Architecture and Integration Framework

Figure 7 illustrates the dual-model system architecture and integration framework, showing the workflow from input processing through score generation and feedback delivery.
